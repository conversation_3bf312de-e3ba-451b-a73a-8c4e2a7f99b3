# Claude 4 Sonnet Translation System - Usage Guide

## Quick Start

The new Claude 4 translation system is now fully integrated into your existing pipeline. Here's how to use it:

### 1. Basic Usage (Automatic)

The system automatically activates when you run your normal translation process:

```bash
node app/2translate/translate.js
```

The system will:
- ✅ **Automatically detect scenes** instead of using fixed chunks
- ✅ **Capture screenshots** from video files for visual context
- ✅ **Apply iterative corrections** to improve translation quality
- ✅ **Maintain character consistency** across the episode
- ✅ **Fall back to original method** if Claude 4 fails

### 2. Testing the System

Run the comprehensive test suite:

```bash
node app/2translate/test-claude4.js
```

This will test all components and show you a sample translation.

### 3. Configuration

Edit `app/2translate/config.js` to customize behavior:

```javascript
export const CLAUDE4_CONFIG = {
  // Enable/disable features
  enableScreenshots: true,     // Use video screenshots
  enableCorrection: true,      // Apply iterative improvements
  enableFallback: true,        // Fall back to original method
  
  // Scene detection sensitivity
  sceneDetection: {
    minSceneLength: 5,         // Minimum lines per scene
    maxSceneLength: 80,        // Maximum lines per scene
    timingGapThreshold: 5000,  // Gap threshold (ms)
  },
  
  // Translation quality
  correction: {
    maxIterations: 2,          // Max improvement passes
    qualityThreshold: 0.8,     // Stop when quality reached
  }
};
```

## Key Features in Action

### 🎯 Intelligent Scene Detection

**Before (Fixed Chunks):**
```
Chunk 1: Lines 1-60 (arbitrary cut)
Chunk 2: Lines 61-120 (mid-conversation)
```

**After (Scene-Aware):**
```
Scene 1: Narrator introduction + Character meeting (6 lines)
Scene 2: Conversation about festival (4 lines)  
Scene 3: Planning and goodbye (3 lines)
```

### 🖼️ Visual Context Integration

When video files are available, the system:
- Captures screenshots at scene timestamps
- Provides visual context to Claude 4
- Better handles visual gags and cultural references
- Improves translation of scene-specific dialogue

### 🔄 Iterative Correction

Each translation goes through quality analysis:

```
Initial Translation → Quality Analysis → Improvements → Final Translation
     ↓                      ↓                ↓              ↓
"Rough draft"         Grammar: 0.7      "Fixed issues"   Grammar: 0.9
                      Natural: 0.6                       Natural: 0.8
                      Accuracy: 0.8                      Accuracy: 0.9
```

### 🎭 Character Consistency

The system tracks:
- **Speech patterns** (formal/informal)
- **Honorific usage** (-san, -chan, -kun)
- **Terminology preferences** (consistent translations)
- **Character relationships** (affects formality)

## Translation Quality Examples

### Sample Input:
```
akira: Hey, Yuki-chan! Are you ready for the school festival?
yuki: Yes, Akira-kun! I've been preparing for weeks.
```

### Claude 4 Output:
```
akira: Hej, Yuki-chan! Jesteś gotowa na festiwal szkolny?
yuki: Tak, Akira-kun! Przygotowuję się od tygodni.
```

**Quality Improvements:**
- ✅ Preserved Japanese honorifics (-chan, -kun)
- ✅ Natural Polish sentence structure
- ✅ Appropriate informal tone for friends
- ✅ Cultural context maintained

## Performance Comparison

| Metric | Original System | Claude 4 System |
|--------|----------------|-----------------|
| **Chunking** | Fixed 60 lines | Intelligent scenes |
| **Context** | None | Full episode context |
| **Quality** | Single pass | Iterative improvement |
| **Consistency** | Basic | Character-aware |
| **Visual Context** | None | Screenshot analysis |
| **Fallback** | None | Automatic fallback |

## Troubleshooting

### Common Issues

1. **"Translation failed" error**
   - System automatically falls back to original method
   - Check API key in .env file
   - Verify internet connection

2. **No video file found**
   - Screenshots disabled automatically
   - Translation continues without visual context
   - Ensure video files are in `app/0rss/downloads/`

3. **Slow performance**
   - Disable screenshots: `enableScreenshots: false`
   - Reduce correction iterations: `maxIterations: 1`
   - Use development config: `NODE_ENV=development`

### Debug Mode

Enable detailed logging:

```bash
NODE_ENV=development node app/2translate/translate.js
```

### Performance Optimization

For faster processing:

```javascript
const translator = new Claude4Translator({
  enableScreenshots: false,    // Skip video analysis
  enableCorrection: false,     // Skip quality improvements
  maxRetries: 1               // Reduce retry attempts
});
```

## Integration with Existing Pipeline

The Claude 4 system integrates seamlessly:

```
1. File Processing (unchanged)
   ↓
2. Claude 4 Translation (NEW)
   ├── Scene Detection
   ├── Screenshot Capture
   ├── Context-Aware Translation
   └── Iterative Correction
   ↓
3. Fallback (if needed)
   ↓
4. Output Processing (unchanged)
```

## Advanced Usage

### Custom Scene Detection

```javascript
const sceneDetector = new SceneDetector({
  speakerChangeWeight: 0.4,    // Emphasize speaker changes
  contentWeight: 0.2,          // De-emphasize content similarity
  timingWeight: 0.4           // Emphasize timing gaps
});
```

### Screenshot Analysis

```javascript
const screenshotTool = new ScreenshotTool({
  quality: 1,                 // Higher quality (slower)
  maxWidth: 1920,            // Full HD screenshots
  enableAnalysis: true       // Analyze image content
});
```

### Translation Correction

```javascript
const correctionTool = new CorrectionTool({
  maxIterations: 3,          // More improvement passes
  qualityThreshold: 0.9,     // Higher quality target
  enableCulturalAdaptation: true
});
```

## Monitoring and Analytics

### Translation Statistics

The system provides detailed statistics:

```javascript
const stats = translator.contextManager.getStatistics();
console.log(`
  Total scenes: ${stats.totalScenes}
  Unique characters: ${stats.uniqueCharacters}
  Average scene length: ${stats.averageSceneLength}
  Terminology entries: ${stats.terminologyEntries}
`);
```

### Quality Metrics

Each translation includes quality scores:
- **Grammar**: Polish grammar correctness
- **Naturalness**: How natural it sounds
- **Accuracy**: Meaning preservation
- **Consistency**: Character/terminology consistency
- **Cultural Adaptation**: Polish audience appropriateness

## Best Practices

1. **Keep video files** in the downloads directory for visual context
2. **Monitor quality scores** to identify problematic translations
3. **Use development mode** for testing and debugging
4. **Customize configuration** based on your specific needs
5. **Check fallback logs** to identify system issues

## Support

For issues or questions:
1. Check the logs in `app/logs/`
2. Run the test suite: `node app/2translate/test-claude4.js`
3. Enable debug mode for detailed output
4. Review the configuration in `app/2translate/config.js`

The system is designed to be robust and self-healing, automatically falling back to the original translation method if any issues occur.
