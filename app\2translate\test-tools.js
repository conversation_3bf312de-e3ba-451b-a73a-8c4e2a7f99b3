// @ts-check

/**
 * Test script for the new translation tools
 * 
 * This script tests the functionality of:
 * - SecondLanguageValidator
 * - ExamplesReference
 * - MetadataPersistence
 */

import { SecondLanguageValidator } from './tools/second-language-validator.js';
import { ExamplesReference } from './tools/examples-reference.js';
import { MetadataPersistence } from './tools/metadata-persistence.js';

// Color constants for better log readability
const COLORS = {
  RESET: '\x1b[0m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  RED: '\x1b[31m',
  SUCCESS: '\x1b[32m\x1b[1m',
  WARNING: '\x1b[33m\x1b[1m',
  ERROR: '\x1b[31m\x1b[1m',
  INFO: '\x1b[36m',
  TEST: '\x1b[35m\x1b[1m'
};

async function testSecondLanguageValidator() {
  console.log(`${COLORS.TEST}🧪 [Test] Testing SecondLanguageValidator...${COLORS.RESET}`);
  
  try {
    const validator = new SecondLanguageValidator({
      enableValidation: true,
      validationThreshold: 0.6
    });

    // Test with a simple translation
    const originalText = "Hello, how are you today?";
    const polishTranslation = "Cześć, jak się masz dzisiaj?";
    
    const result = await validator.validateTranslation(originalText, polishTranslation, {
      animeTitle: "Test Anime",
      episode: "01",
      sceneType: "dialogue"
    });

    console.log(`${COLORS.INFO}📊 [Test] Validation result:${COLORS.RESET}`);
    console.log(`  - Valid: ${result.isValid}`);
    console.log(`  - Confidence: ${result.confidence}`);
    console.log(`  - Used second language: ${result.usedSecondLanguage}`);
    console.log(`  - Issues: ${result.issues.length}`);
    
    console.log(`${COLORS.SUCCESS}✅ [Test] SecondLanguageValidator test completed${COLORS.RESET}`);
    return true;
    
  } catch (error) {
    console.error(`${COLORS.ERROR}❌ [Test] SecondLanguageValidator test failed: ${error.message}${COLORS.RESET}`);
    return false;
  }
}

async function testExamplesReference() {
  console.log(`${COLORS.TEST}🧪 [Test] Testing ExamplesReference...${COLORS.RESET}`);
  
  try {
    const examplesRef = new ExamplesReference({
      enableExampleLookup: true,
      examplesPath: 'app/2translate/examples.xml',
      maxRelevantExamples: 3
    });

    // Test finding examples
    const originalText = "Thank you very much!";
    
    const result = await examplesRef.findRelevantExamples(originalText, "", {
      animeTitle: "Test Anime",
      episode: "01"
    });

    console.log(`${COLORS.INFO}📊 [Test] Examples lookup result:${COLORS.RESET}`);
    console.log(`  - Has examples: ${result.hasExamples}`);
    console.log(`  - Relevant examples found: ${result.relevantExamples.length}`);
    
    if (result.relevantExamples.length > 0) {
      console.log(`  - First example: ${result.relevantExamples[0].englishSource.substring(0, 50)}...`);
    }

    // Test statistics
    const stats = examplesRef.getStatistics();
    console.log(`${COLORS.INFO}📈 [Test] Examples statistics:${COLORS.RESET}`);
    console.log(`  - Total examples: ${stats.totalExamples}`);
    console.log(`  - Indexed terms: ${stats.indexedTerms}`);
    console.log(`  - Average complexity: ${stats.averageComplexity.toFixed(2)}`);
    
    console.log(`${COLORS.SUCCESS}✅ [Test] ExamplesReference test completed${COLORS.RESET}`);
    return true;
    
  } catch (error) {
    console.error(`${COLORS.ERROR}❌ [Test] ExamplesReference test failed: ${error.message}${COLORS.RESET}`);
    return false;
  }
}

async function testMetadataPersistence() {
  console.log(`${COLORS.TEST}🧪 [Test] Testing MetadataPersistence...${COLORS.RESET}`);
  
  try {
    const metadata = new MetadataPersistence({
      enablePersistence: true,
      metadataDirectory: 'app/2translate/metadata',
      autoSave: false // Disable auto-save for testing
    });

    // Test anime metadata
    const animeTitle = "Test Anime Series";
    const animeData = metadata.getAnimeMetadata(animeTitle, {
      genres: ["Action", "Comedy"],
      totalEpisodes: 12
    });

    console.log(`${COLORS.INFO}📊 [Test] Anime metadata:${COLORS.RESET}`);
    console.log(`  - Title: ${animeData.title}`);
    console.log(`  - Genres: ${animeData.genres.join(', ')}`);
    console.log(`  - Episodes: ${animeData.totalEpisodes}`);

    // Test episode data
    metadata.addEpisodeData(animeTitle, "01", {
      scenes: 15,
      lines: 120,
      quality: 0.85,
      characters: ["Protagonist", "Deuteragonist"]
    });

    // Test character info
    metadata.updateCharacterInfo(animeTitle, {
      "Protagonist": {
        gender: "male",
        personality: "cheerful",
        speechPattern: "casual"
      }
    });

    // Test terminology
    metadata.updateTerminology(animeTitle, {
      "magic": "magia",
      "sword": "miecz",
      "guild": "gildia"
    });

    // Get statistics
    const stats = metadata.getStatistics();
    console.log(`${COLORS.INFO}📈 [Test] Metadata statistics:${COLORS.RESET}`);
    console.log(`  - Anime count: ${stats.animeCount}`);
    console.log(`  - Character databases: ${stats.characterDatabaseCount}`);
    console.log(`  - Terminology databases: ${stats.terminologyDatabaseCount}`);
    console.log(`  - Total episodes: ${stats.totalEpisodes}`);

    console.log(`${COLORS.SUCCESS}✅ [Test] MetadataPersistence test completed${COLORS.RESET}`);
    return true;
    
  } catch (error) {
    console.error(`${COLORS.ERROR}❌ [Test] MetadataPersistence test failed: ${error.message}${COLORS.RESET}`);
    return false;
  }
}

async function runAllTests() {
  console.log(`${COLORS.TEST}🚀 [Test] Starting tool tests...${COLORS.RESET}`);
  
  const results = [];
  
  // Run all tests
  results.push(await testSecondLanguageValidator());
  results.push(await testExamplesReference());
  results.push(await testMetadataPersistence());
  
  // Summary
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log(`${COLORS.TEST}📊 [Test] Test Summary:${COLORS.RESET}`);
  console.log(`  - Passed: ${passed}/${total}`);
  
  if (passed === total) {
    console.log(`${COLORS.SUCCESS}🎉 [Test] All tests passed!${COLORS.RESET}`);
  } else {
    console.log(`${COLORS.WARNING}⚠️  [Test] Some tests failed${COLORS.RESET}`);
  }
  
  return passed === total;
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error(`${COLORS.ERROR}❌ [Test] Test execution failed: ${error.message}${COLORS.RESET}`);
      process.exit(1);
    });
}

export { runAllTests };
