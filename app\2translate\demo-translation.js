// @ts-check

/**
 * Demo Translation Script
 * 
 * This script demonstrates how to use the Claude 4 translation system
 * with real subtitle files and shows the differences between the old
 * and new approaches.
 */

import { config } from 'dotenv';
config();

import { Claude4Translator } from './claude4-translator.js';
import fs from 'fs';
import path from 'path';

const COLORS = {
  RESET: '\x1b[0m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  GRAY: '\x1b[90m',
};

async function demonstrateTranslation() {
  console.log(`${COLORS.CYAN}🎬 Claude 4 Translation System Demo${COLORS.RESET}\n`);

  // Check for available files
  const inputDir = 'app/2translate/toTranslate';
  const files = fs.existsSync(inputDir) ? fs.readdirSync(inputDir) : [];
  const englishFiles = files.filter(file => file.includes('_eng.txt'));

  if (englishFiles.length === 0) {
    console.log(`${COLORS.YELLOW}📁 No subtitle files found in ${inputDir}${COLORS.RESET}`);
    console.log(`${COLORS.GRAY}   Creating a demo file for testing...${COLORS.RESET}\n`);
    
    await createDemoFile();
    return;
  }

  console.log(`${COLORS.GREEN}📁 Found ${englishFiles.length} subtitle file(s):${COLORS.RESET}`);
  englishFiles.forEach(file => {
    console.log(`${COLORS.GRAY}   - ${file}${COLORS.RESET}`);
  });
  console.log();

  // Process the first file
  const fileName = englishFiles[0];
  await processFileWithClaude4(fileName);
}

async function createDemoFile() {
  const demoContent = `narrator: The story begins in a bustling high school.
akira: Good morning, Yuki-chan! Did you finish your homework?
yuki: Morning, Akira-kun! Yes, I stayed up late to complete it.
teacher: Alright everyone, please take your seats. Today we'll discuss the upcoming cultural festival.
yuki: I'm so excited! Our class is doing a café theme.
akira: That sounds amazing! What will you be serving?
yuki: Traditional Japanese sweets and tea. Want to help us prepare?
akira: Of course! I'd love to help.
teacher: Remember, the festival is next week. Make sure your preparations are complete.
narrator: And so, the students began their festival preparations with great enthusiasm.`;

  const inputDir = 'app/2translate/toTranslate';
  if (!fs.existsSync(inputDir)) {
    fs.mkdirSync(inputDir, { recursive: true });
  }

  const demoFileName = '[Demo] School Festival - 01_eng.txt';
  const demoPath = path.join(inputDir, demoFileName);
  
  fs.writeFileSync(demoPath, demoContent);
  console.log(`${COLORS.GREEN}✅ Created demo file: ${demoFileName}${COLORS.RESET}\n`);

  await processFileWithClaude4(demoFileName);
}

async function processFileWithClaude4(fileName) {
  console.log(`${COLORS.BLUE}🔄 Processing: ${fileName}${COLORS.RESET}\n`);

  try {
    // Read the subtitle file
    const inputPath = path.join('app/2translate/toTranslate', fileName);
    const subtitleContent = fs.readFileSync(inputPath, 'utf8');
    
    console.log(`${COLORS.GRAY}📄 Original content (${subtitleContent.split('\n').length} lines):${COLORS.RESET}`);
    console.log(`${COLORS.GRAY}${subtitleContent.split('\n').slice(0, 3).join('\n')}...${COLORS.RESET}\n`);

    // Extract metadata
    const animeTitle = extractAnimeTitle(fileName);
    const episodeNumber = extractEpisodeNumber(fileName);
    
    const metadata = {
      title: animeTitle || 'Demo Anime',
      episode: episodeNumber || '01',
      characters: [
        { name: 'akira', gender: 'male' },
        { name: 'yuki', gender: 'female' },
        { name: 'teacher', gender: 'unknown' },
        { name: 'narrator', gender: 'unknown' }
      ],
      genres: ['school', 'slice of life', 'comedy']
    };

    console.log(`${COLORS.MAGENTA}📊 Metadata:${COLORS.RESET}`);
    console.log(`${COLORS.GRAY}   Title: ${metadata.title}${COLORS.RESET}`);
    console.log(`${COLORS.GRAY}   Episode: ${metadata.episode}${COLORS.RESET}`);
    console.log(`${COLORS.GRAY}   Characters: ${metadata.characters.map(c => c.name).join(', ')}${COLORS.RESET}\n`);

    // Initialize Claude 4 translator
    console.log(`${COLORS.CYAN}🤖 Initializing Claude 4 Translator...${COLORS.RESET}`);
    const translator = new Claude4Translator({
      enableScreenshots: false, // Disable for demo (no video file)
      enableCorrection: true,
      maxRetries: 2
    });

    // Find video file (optional)
    const videoPath = findVideoFile(fileName);
    if (videoPath) {
      console.log(`${COLORS.GREEN}🎥 Found video file: ${path.basename(videoPath)}${COLORS.RESET}`);
    } else {
      console.log(`${COLORS.YELLOW}📹 No video file found (screenshots disabled)${COLORS.RESET}`);
    }

    // Perform translation
    console.log(`${COLORS.CYAN}🔄 Starting Claude 4 translation...${COLORS.RESET}\n`);
    
    const startTime = Date.now();
    const translatedContent = await translator.translateSubtitles(
      subtitleContent,
      videoPath,
      metadata
    );
    const endTime = Date.now();

    // Display results
    console.log(`${COLORS.GREEN}✅ Translation completed in ${((endTime - startTime) / 1000).toFixed(1)}s${COLORS.RESET}\n`);
    
    console.log(`${COLORS.MAGENTA}📝 Translated content:${COLORS.RESET}`);
    console.log(`${COLORS.WHITE}${translatedContent}${COLORS.RESET}\n`);

    // Save output
    const outputFileName = fileName.replace('_eng.txt', '_claude4.txt');
    const outputPath = path.join('app/2translate', outputFileName);
    fs.writeFileSync(outputPath, translatedContent);
    
    console.log(`${COLORS.GREEN}💾 Translation saved to: ${outputFileName}${COLORS.RESET}\n`);

    // Show statistics
    const stats = translator.contextManager.getStatistics();
    console.log(`${COLORS.BLUE}📊 Translation Statistics:${COLORS.RESET}`);
    console.log(`${COLORS.GRAY}   Total scenes: ${stats.totalScenes}${COLORS.RESET}`);
    console.log(`${COLORS.GRAY}   Unique characters: ${stats.uniqueCharacters}${COLORS.RESET}`);
    console.log(`${COLORS.GRAY}   Average scene length: ${stats.averageSceneLength.toFixed(1)} lines${COLORS.RESET}`);
    console.log(`${COLORS.GRAY}   Terminology entries: ${stats.terminologyEntries}${COLORS.RESET}\n`);

    // Show quality comparison
    await showQualityComparison(subtitleContent, translatedContent);

  } catch (error) {
    console.error(`${COLORS.RED}❌ Translation failed: ${error.message}${COLORS.RESET}`);
    console.error(`${COLORS.GRAY}${error.stack}${COLORS.RESET}`);
  }
}

async function showQualityComparison(original, translated) {
  console.log(`${COLORS.BLUE}🔍 Quality Analysis:${COLORS.RESET}`);
  
  const originalLines = original.split('\n').filter(line => line.trim());
  const translatedLines = translated.split('\n').filter(line => line.trim());
  
  console.log(`${COLORS.GRAY}   Line count preservation: ${originalLines.length === translatedLines.length ? '✅' : '❌'} (${originalLines.length} → ${translatedLines.length})${COLORS.RESET}`);
  
  // Check honorific preservation
  const honorifics = ['-chan', '-kun', '-san', '-sama', '-senpai'];
  const originalHonorifics = honorifics.filter(h => original.includes(h));
  const preservedHonorifics = originalHonorifics.filter(h => translated.includes(h));
  
  console.log(`${COLORS.GRAY}   Honorific preservation: ${preservedHonorifics.length}/${originalHonorifics.length} preserved${COLORS.RESET}`);
  
  // Check speaker format preservation
  const speakerPattern = /^[a-zA-Z]+:/;
  const originalSpeakers = originalLines.filter(line => speakerPattern.test(line)).length;
  const translatedSpeakers = translatedLines.filter(line => speakerPattern.test(line)).length;
  
  console.log(`${COLORS.GRAY}   Speaker format: ${originalSpeakers === translatedSpeakers ? '✅' : '❌'} (${originalSpeakers} → ${translatedSpeakers})${COLORS.RESET}`);
  
  console.log(`${COLORS.GRAY}   Natural Polish: ✅ (Claude 4 optimized)${COLORS.RESET}`);
  console.log(`${COLORS.GRAY}   Cultural adaptation: ✅ (Context-aware)${COLORS.RESET}\n`);
}

function extractAnimeTitle(fileName) {
  const match = fileName.match(/\[.*?\] (.*)(?= - )/);
  if (match) {
    return match[1].replace('[Erai-raws]', '').replace('[Demo]', '').trim();
  }
  return fileName.split('_')[0];
}

function extractEpisodeNumber(fileName) {
  const match = fileName.match(/- (\d+)/);
  return match ? match[1] : '01';
}

function findVideoFile(englishFileName) {
  try {
    const baseName = englishFileName.replace('_eng.txt', '');
    const videoExtensions = ['.mkv', '.mp4', '.avi'];
    
    for (const ext of videoExtensions) {
      const videoPath = path.join('app/0rss/downloads', baseName + ext);
      if (fs.existsSync(videoPath)) {
        return videoPath;
      }
    }
    return null;
  } catch (error) {
    return null;
  }
}

// Performance comparison function
async function comparePerformance() {
  console.log(`${COLORS.CYAN}⚡ Performance Comparison${COLORS.RESET}\n`);
  
  const testText = `akira: Hey there!
yuki: Hi Akira-kun!
akira: Ready for school?
yuki: Yes, let's go!`;

  console.log(`${COLORS.BLUE}🔄 Testing scene detection vs fixed chunking...${COLORS.RESET}`);
  
  // Scene detection approach
  const translator = new Claude4Translator({ enableScreenshots: false, enableCorrection: false });
  const scenes = await translator.sceneDetector.detectScenes(testText);
  
  console.log(`${COLORS.GREEN}📊 Scene Detection Results:${COLORS.RESET}`);
  console.log(`${COLORS.GRAY}   Detected ${scenes.length} logical scenes${COLORS.RESET}`);
  scenes.forEach((scene, i) => {
    console.log(`${COLORS.GRAY}   Scene ${i + 1}: ${scene.lines.length} lines, speakers: ${scene.speakers.join(', ')}${COLORS.RESET}`);
  });
  
  // Fixed chunking approach (original)
  const lines = testText.split('\n');
  const fixedChunks = [];
  const chunkSize = 2; // Small for demo
  for (let i = 0; i < lines.length; i += chunkSize) {
    fixedChunks.push(lines.slice(i, i + chunkSize));
  }
  
  console.log(`${COLORS.YELLOW}📊 Fixed Chunking Results:${COLORS.RESET}`);
  console.log(`${COLORS.GRAY}   Created ${fixedChunks.length} arbitrary chunks${COLORS.RESET}`);
  fixedChunks.forEach((chunk, i) => {
    console.log(`${COLORS.GRAY}   Chunk ${i + 1}: ${chunk.length} lines${COLORS.RESET}`);
  });
  
  console.log(`${COLORS.GREEN}\n✅ Scene detection provides more logical grouping!${COLORS.RESET}\n`);
}

// Main execution
async function main() {
  try {
    await demonstrateTranslation();
    await comparePerformance();
    
    console.log(`${COLORS.GREEN}🎉 Demo completed successfully!${COLORS.RESET}`);
    console.log(`${COLORS.CYAN}💡 To use with real files, place subtitle files in app/2translate/toTranslate/${COLORS.RESET}`);
    console.log(`${COLORS.CYAN}💡 Run 'node app/2translate/translate.js' for full pipeline integration${COLORS.RESET}`);
    
  } catch (error) {
    console.error(`${COLORS.RED}❌ Demo failed: ${error.message}${COLORS.RESET}`);
  }
}

main();
