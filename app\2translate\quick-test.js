// @ts-check

import { config } from 'dotenv';
config();

import { Claude4Translator } from './claude4-translator.js';

console.log('🧪 Quick Translation Test...');

const testText = `akira: Hey, <PERSON><PERSON>-chan! Are you ready?
yuki: Yes, <PERSON>-kun! Let's go!`;

const metadata = {
  title: 'Test',
  characters: [{ name: 'akira', gender: 'male' }, { name: 'yuki', gender: 'female' }]
};

try {
  const translator = new Claude4Translator({
    enableScreenshots: false,
    enableCorrection: false, // Disable correction to see raw translation
    maxRetries: 1
  });

  console.log('🔄 Starting translation...');
  const result = await translator.translateSubtitles(testText, null, metadata);
  
  console.log('✅ Translation result:');
  console.log(result);
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
}
