// @ts-check

import { config } from 'dotenv';
config();

console.log('🧪 Simple Claude 4 Test Starting...');

try {
  console.log('1. Importing Claude4Translator...');
  const { Claude4Translator } = await import('./claude4-translator.js');
  console.log('✅ Import successful');

  console.log('2. Creating translator instance...');
  const translator = new Claude4Translator({
    enableScreenshots: false,
    enableCorrection: false,
    maxRetries: 1
  });
  console.log('✅ Translator created');

  console.log('3. Testing scene detection...');
  const testText = `akira: Hello there!
yuki: Hi <PERSON>-kun!
akira: How are you?`;

  const scenes = await translator.sceneDetector.detectScenes(testText);
  console.log(`✅ Scene detection works: ${scenes.length} scenes detected`);

  console.log('4. Testing context manager...');
  translator.contextManager.initialize({
    title: 'Test',
    characters: [{ name: 'akira', gender: 'male' }]
  });
  console.log('✅ Context manager works');

  console.log('\n🎉 All basic tests passed!');

} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
}
