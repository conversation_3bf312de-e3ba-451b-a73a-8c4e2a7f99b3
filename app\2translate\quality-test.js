// @ts-check

import { config } from 'dotenv';
config();

import { Claude4Translator } from './claude4-translator.js';

console.log('🧪 Quality Score Test...');

const testText = `akira: Hey, <PERSON><PERSON><PERSON>chan! Are you ready for the school festival?
yuki: Yes, <PERSON>-kun! I've been preparing for weeks.
akira: That's great! What's your club doing?`;

const metadata = {
  title: 'Test Anime',
  characters: [{ name: 'akira', gender: 'male' }, { name: 'yuki', gender: 'female' }]
};

try {
  const translator = new Claude4Translator({
    enableScreenshots: false,
    enableCorrection: true, // Enable correction to see quality scores
    maxRetries: 1
  });

  console.log('🔄 Starting translation with correction...');
  const result = await translator.translateSubtitles(testText, null, metadata);
  
  console.log('✅ Final translation result:');
  console.log(result);
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
}
