// @ts-check

/**
 * Production Test Suite - Real World Scenario Simulation
 * 
 * This test simulates the complete production workflow from subtitle extraction
 * through translation, testing all tools with the first 60 lines of text.
 * 
 * Test Flow:
 * 1. Create mock MKV file structure
 * 2. Simulate subtitle extraction (English + French)
 * 3. Run scene detection and context analysis
 * 4. Execute translation with all tools (first 60 lines only)
 * 5. Test second language validation
 * 6. Test examples reference
 * 7. Test metadata persistence
 * 8. Generate comprehensive report
 * 
 * Usage: node app/production-test.js
 */

import fs from 'fs';
import path from 'path';
import { Claude4Translator } from './2translate/claude4-translator.js';
import { SecondLanguageValidator } from './2translate/tools/second-language-validator.js';
import { ExamplesReference } from './2translate/tools/examples-reference.js';
import { MetadataPersistence } from './2translate/tools/metadata-persistence.js';

// Color constants for better log readability
const COLORS = {
  RESET: '\x1b[0m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  RED: '\x1b[31m',
  GRAY: '\x1b[90m',
  SUCCESS: '\x1b[32m\x1b[1m',
  WARNING: '\x1b[33m\x1b[1m',
  ERROR: '\x1b[31m\x1b[1m',
  INFO: '\x1b[36m',
  TEST: '\x1b[35m\x1b[1m',
  PRODUCTION: '\x1b[36m\x1b[1m'
};

class ProductionTest {
  constructor() {
    this.testResults = {
      subtitleExtraction: false,
      sceneDetection: false,
      translation: false,
      secondLanguageValidation: false,
      examplesReference: false,
      metadataPersistence: false,
      overallSuccess: false
    };
    
    this.testData = {
      animeTitle: "Test Anime Series",
      episode: "01",
      fileName: "test_anime_01_eng.txt",
      sampleLines: 60
    };
    
    this.startTime = Date.now();
  }

  /**
   * Run the complete production test suite
   */
  async runProductionTest() {
    console.log(`${COLORS.PRODUCTION}🚀 [Production Test] Starting real-world scenario simulation...${COLORS.RESET}`);
    console.log(`${COLORS.INFO}📋 [Production Test] Testing with ${this.testData.sampleLines} lines from ${this.testData.animeTitle} Episode ${this.testData.episode}${COLORS.RESET}`);
    
    try {
      // Step 1: Setup test environment
      await this.setupTestEnvironment();
      
      // Step 2: Simulate subtitle extraction
      await this.testSubtitleExtraction();
      
      // Step 3: Test scene detection and context analysis
      await this.testSceneDetection();
      
      // Step 4: Test translation with all tools
      await this.testTranslation();
      
      // Step 5: Test second language validation
      await this.testSecondLanguageValidation();
      
      // Step 6: Test examples reference
      await this.testExamplesReference();
      
      // Step 7: Test metadata persistence
      await this.testMetadataPersistence();
      
      // Step 8: Generate final report
      await this.generateReport();
      
    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Production Test] Critical error: ${error.message}${COLORS.RESET}`);
      this.testResults.overallSuccess = false;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Setup test environment with mock data
   */
  async setupTestEnvironment() {
    console.log(`${COLORS.TEST}🔧 [Production Test] Setting up test environment...${COLORS.RESET}`);
    
    // Ensure test directories exist
    const testDirs = [
      'app/2translate/toTranslate',
      'app/2translate/metadata',
      'app/1clear/extracted'
    ];
    
    for (const dir of testDirs) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    }
    
    // Create sample English subtitle content (60 lines)
    const sampleEnglishContent = this.generateSampleSubtitleContent('english');
    const englishPath = `app/2translate/toTranslate/${this.testData.fileName}`;
    fs.writeFileSync(englishPath, sampleEnglishContent);
    
    // Create sample French subtitle content for second language validation
    const sampleFrenchContent = this.generateSampleSubtitleContent('french');
    const frenchPath = `app/2translate/toTranslate/${this.testData.fileName.replace('_eng.txt', '_other.txt')}`;
    fs.writeFileSync(frenchPath, sampleFrenchContent);
    
    console.log(`${COLORS.SUCCESS}✅ [Production Test] Test environment setup complete${COLORS.RESET}`);
  }

  /**
   * Generate sample subtitle content for testing
   */
  generateSampleSubtitleContent(language) {
    const englishLines = [
      "Narrator | Welcome to the world of magic and adventure.",
      "Protagonist | I never thought I'd see a place like this!",
      "Deuteragonist | This is just the beginning of our journey.",
      "Protagonist | What should we do first?",
      "Mentor | First, you must learn to control your power.",
      "Protagonist | I'm ready to learn whatever it takes.",
      "Antagonist | You think you can defeat me so easily?",
      "Protagonist | I won't give up, no matter what!",
      "Friend | We believe in you! You can do this!",
      "Protagonist | Thank you, everyone. Let's do this together!",
      "Narrator | And so their adventure truly began...",
      "Protagonist | This magic feels incredible!",
      "Mentor | Remember, with great power comes great responsibility.",
      "Deuteragonist | I'll always be by your side.",
      "Protagonist | I promise to protect everyone I care about.",
      "Villain | Your determination is admirable, but futile.",
      "Protagonist | I'll prove you wrong!",
      "Friend | The power of friendship will guide us!",
      "Protagonist | Together, we're unstoppable!",
      "Narrator | Their bonds grew stronger with each challenge."
    ];

    const frenchLines = [
      "Narrateur | Bienvenue dans le monde de la magie et de l'aventure.",
      "Protagoniste | Je n'aurais jamais pensé voir un endroit comme celui-ci !",
      "Deutéragoniste | Ce n'est que le début de notre voyage.",
      "Protagoniste | Que devrions-nous faire en premier ?",
      "Mentor | D'abord, tu dois apprendre à contrôler ton pouvoir.",
      "Protagoniste | Je suis prêt à apprendre tout ce qu'il faut.",
      "Antagoniste | Tu penses pouvoir me vaincre si facilement ?",
      "Protagoniste | Je n'abandonnerai pas, quoi qu'il arrive !",
      "Ami | Nous croyons en toi ! Tu peux le faire !",
      "Protagoniste | Merci à tous. Faisons cela ensemble !",
      "Narrateur | Et ainsi leur aventure commença vraiment...",
      "Protagoniste | Cette magie est incroyable !",
      "Mentor | Souviens-toi, un grand pouvoir implique de grandes responsabilités.",
      "Deutéragoniste | Je serai toujours à tes côtés.",
      "Protagoniste | Je promets de protéger tous ceux qui me sont chers.",
      "Méchant | Ta détermination est admirable, mais futile.",
      "Protagoniste | Je vais te prouver le contraire !",
      "Ami | Le pouvoir de l'amitié nous guidera !",
      "Protagoniste | Ensemble, nous sommes imbattables !",
      "Narrateur | Leurs liens se renforcèrent à chaque défi."
    ];

    const lines = language === 'french' ? frenchLines : englishLines;
    
    // Repeat and extend to reach 60 lines
    const extendedLines = [];
    for (let i = 0; i < this.testData.sampleLines; i++) {
      const lineIndex = i % lines.length;
      const lineNumber = Math.floor(i / lines.length) + 1;
      const suffix = lineNumber > 1 ? ` (${lineNumber})` : '';
      extendedLines.push(lines[lineIndex] + suffix);
    }
    
    return extendedLines.join('\n');
  }

  /**
   * Test subtitle extraction simulation
   */
  async testSubtitleExtraction() {
    console.log(`${COLORS.TEST}📄 [Production Test] Testing subtitle extraction simulation...${COLORS.RESET}`);
    
    try {
      // Verify that our mock subtitle files exist and are readable
      const englishPath = `app/2translate/toTranslate/${this.testData.fileName}`;
      const frenchPath = `app/2translate/toTranslate/${this.testData.fileName.replace('_eng.txt', '_other.txt')}`;
      
      if (fs.existsSync(englishPath) && fs.existsSync(frenchPath)) {
        const englishContent = fs.readFileSync(englishPath, 'utf8');
        const frenchContent = fs.readFileSync(frenchPath, 'utf8');
        
        const englishLines = englishContent.split('\n').filter(line => line.trim());
        const frenchLines = frenchContent.split('\n').filter(line => line.trim());
        
        console.log(`${COLORS.INFO}📊 [Production Test] English lines extracted: ${englishLines.length}${COLORS.RESET}`);
        console.log(`${COLORS.INFO}📊 [Production Test] French lines extracted: ${frenchLines.length}${COLORS.RESET}`);
        
        if (englishLines.length >= this.testData.sampleLines && frenchLines.length >= this.testData.sampleLines) {
          this.testResults.subtitleExtraction = true;
          console.log(`${COLORS.SUCCESS}✅ [Production Test] Subtitle extraction simulation successful${COLORS.RESET}`);
        } else {
          throw new Error(`Insufficient lines extracted. Expected: ${this.testData.sampleLines}, Got: EN=${englishLines.length}, FR=${frenchLines.length}`);
        }
      } else {
        throw new Error('Mock subtitle files not found');
      }
      
    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Production Test] Subtitle extraction failed: ${error.message}${COLORS.RESET}`);
      this.testResults.subtitleExtraction = false;
    }
  }

  /**
   * Test scene detection and context analysis
   */
  async testSceneDetection() {
    console.log(`${COLORS.TEST}🎬 [Production Test] Testing scene detection and context analysis...${COLORS.RESET}`);

    try {
      const translator = new Claude4Translator();
      const englishPath = `app/2translate/toTranslate/${this.testData.fileName}`;
      const content = fs.readFileSync(englishPath, 'utf8');

      // Test scene detection
      const scenes = await translator.sceneDetector.detectScenes(content);

      console.log(`${COLORS.INFO}📊 [Production Test] Scenes detected: ${scenes.length}${COLORS.RESET}`);

      if (scenes.length > 0) {
        console.log(`${COLORS.INFO}📋 [Production Test] First scene: ${scenes[0].lines.length} lines, tone: ${scenes[0].emotionalTone}${COLORS.RESET}`);
        this.testResults.sceneDetection = true;
        console.log(`${COLORS.SUCCESS}✅ [Production Test] Scene detection successful${COLORS.RESET}`);
      } else {
        throw new Error('No scenes detected');
      }

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Production Test] Scene detection failed: ${error.message}${COLORS.RESET}`);
      this.testResults.sceneDetection = false;
    }
  }

  /**
   * Test translation with all tools (first 60 lines only)
   */
  async testTranslation() {
    console.log(`${COLORS.TEST}🔄 [Production Test] Testing translation with all tools (${this.testData.sampleLines} lines)...${COLORS.RESET}`);

    try {
      const translator = new Claude4Translator();
      const englishPath = `app/2translate/toTranslate/${this.testData.fileName}`;
      const content = fs.readFileSync(englishPath, 'utf8');

      // Limit to first 60 lines
      const lines = content.split('\n').slice(0, this.testData.sampleLines);
      const limitedContent = lines.join('\n');

      console.log(`${COLORS.INFO}📝 [Production Test] Translating ${lines.length} lines...${COLORS.RESET}`);

      // Prepare metadata
      const metadata = {
        title: this.testData.animeTitle,
        episode: this.testData.episode,
        characters: [
          { name: 'Protagonist', gender: 'male' },
          { name: 'Deuteragonist', gender: 'female' },
          { name: 'Mentor', gender: 'male' }
        ],
        genres: ['Adventure', 'Fantasy', 'Action']
      };

      // Run translation
      const translatedContent = await translator.translateSubtitles(
        limitedContent,
        null, // No video path for test
        metadata,
        this.testData.fileName
      );

      if (translatedContent && translatedContent.length > 0) {
        const translatedLines = translatedContent.split('\n').filter(line => line.trim());
        console.log(`${COLORS.INFO}📊 [Production Test] Translation completed: ${translatedLines.length} lines${COLORS.RESET}`);
        console.log(`${COLORS.INFO}📋 [Production Test] Sample translation: ${translatedLines[0]?.substring(0, 80)}...${COLORS.RESET}`);

        this.testResults.translation = true;
        console.log(`${COLORS.SUCCESS}✅ [Production Test] Translation successful${COLORS.RESET}`);
      } else {
        throw new Error('Translation returned empty content');
      }

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Production Test] Translation failed: ${error.message}${COLORS.RESET}`);
      this.testResults.translation = false;
    }
  }

  /**
   * Test second language validation
   */
  async testSecondLanguageValidation() {
    console.log(`${COLORS.TEST}🔍 [Production Test] Testing second language validation...${COLORS.RESET}`);

    try {
      const validator = new SecondLanguageValidator({
        enableValidation: true,
        validationThreshold: 0.6,
        secondLanguageDirectory: 'app/2translate/toTranslate'
      });

      // Test with sample translation
      const originalText = "Welcome to the world of magic and adventure.";
      const polishTranslation = "Witamy w świecie magii i przygód.";

      const result = await validator.validateTranslation(originalText, polishTranslation, {
        fileName: this.testData.fileName,
        animeTitle: this.testData.animeTitle,
        episode: this.testData.episode,
        sceneType: 'narrative'
      });

      console.log(`${COLORS.INFO}📊 [Production Test] Validation result: Valid=${result.isValid}, Confidence=${result.confidence.toFixed(2)}${COLORS.RESET}`);
      console.log(`${COLORS.INFO}📋 [Production Test] Used second language: ${result.usedSecondLanguage}${COLORS.RESET}`);

      if (result.confidence !== undefined) {
        this.testResults.secondLanguageValidation = true;
        console.log(`${COLORS.SUCCESS}✅ [Production Test] Second language validation successful${COLORS.RESET}`);
      } else {
        throw new Error('Validation returned undefined confidence');
      }

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Production Test] Second language validation failed: ${error.message}${COLORS.RESET}`);
      this.testResults.secondLanguageValidation = false;
    }
  }

  /**
   * Test examples reference system
   */
  async testExamplesReference() {
    console.log(`${COLORS.TEST}📚 [Production Test] Testing examples reference system...${COLORS.RESET}`);

    try {
      const examplesRef = new ExamplesReference({
        enableExampleLookup: true,
        examplesPath: 'app/2translate/examples.xml',
        maxRelevantExamples: 3
      });

      // Test finding examples
      const queryText = "Thank you very much!";
      const result = await examplesRef.findRelevantExamples(queryText, "", {
        animeTitle: this.testData.animeTitle,
        episode: this.testData.episode
      });

      console.log(`${COLORS.INFO}📊 [Production Test] Examples found: ${result.relevantExamples.length}${COLORS.RESET}`);
      console.log(`${COLORS.INFO}📋 [Production Test] Has examples: ${result.hasExamples}${COLORS.RESET}`);

      // Get statistics
      const stats = examplesRef.getStatistics();
      console.log(`${COLORS.INFO}📈 [Production Test] Total examples in database: ${stats.totalExamples}${COLORS.RESET}`);
      console.log(`${COLORS.INFO}📈 [Production Test] Indexed terms: ${stats.indexedTerms}${COLORS.RESET}`);

      if (stats.totalExamples >= 0) { // Even 0 examples is a valid result
        this.testResults.examplesReference = true;
        console.log(`${COLORS.SUCCESS}✅ [Production Test] Examples reference system successful${COLORS.RESET}`);
      } else {
        throw new Error('Examples reference returned invalid statistics');
      }

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Production Test] Examples reference failed: ${error.message}${COLORS.RESET}`);
      this.testResults.examplesReference = false;
    }
  }

  /**
   * Test metadata persistence system
   */
  async testMetadataPersistence() {
    console.log(`${COLORS.TEST}💾 [Production Test] Testing metadata persistence system...${COLORS.RESET}`);

    try {
      const metadata = new MetadataPersistence({
        enablePersistence: true,
        metadataDirectory: 'app/2translate/metadata',
        autoSave: false // Disable auto-save for testing
      });

      // Test anime metadata
      const animeData = metadata.getAnimeMetadata(this.testData.animeTitle, {
        genres: ["Adventure", "Fantasy"],
        totalEpisodes: 12
      });

      console.log(`${COLORS.INFO}📊 [Production Test] Anime metadata created: ${animeData.title}${COLORS.RESET}`);

      // Test episode data
      metadata.addEpisodeData(this.testData.animeTitle, this.testData.episode, {
        scenes: 5,
        lines: this.testData.sampleLines,
        quality: 0.85,
        characters: ["Protagonist", "Deuteragonist", "Mentor"]
      });

      // Test character info
      metadata.updateCharacterInfo(this.testData.animeTitle, {
        "Protagonist": {
          gender: "male",
          personality: "determined",
          speechPattern: "casual"
        }
      });

      // Get statistics
      const stats = metadata.getStatistics();
      console.log(`${COLORS.INFO}📈 [Production Test] Metadata statistics: ${stats.animeCount} anime, ${stats.totalEpisodes} episodes${COLORS.RESET}`);

      if (stats.animeCount > 0) {
        this.testResults.metadataPersistence = true;
        console.log(`${COLORS.SUCCESS}✅ [Production Test] Metadata persistence successful${COLORS.RESET}`);
      } else {
        throw new Error('Metadata persistence returned no anime count');
      }

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Production Test] Metadata persistence failed: ${error.message}${COLORS.RESET}`);
      this.testResults.metadataPersistence = false;
    }
  }

  /**
   * Generate comprehensive test report
   */
  async generateReport() {
    console.log(`${COLORS.PRODUCTION}📊 [Production Test] Generating comprehensive test report...${COLORS.RESET}`);

    const endTime = Date.now();
    const duration = (endTime - this.startTime) / 1000;

    // Calculate overall success
    const passedTests = Object.values(this.testResults).filter(result => result === true).length;
    const totalTests = Object.keys(this.testResults).length - 1; // Exclude overallSuccess
    this.testResults.overallSuccess = passedTests === totalTests;

    // Generate report
    const report = {
      testSuite: "Production Test Suite - Real World Scenario",
      timestamp: new Date().toISOString(),
      duration: `${duration.toFixed(2)} seconds`,
      testData: this.testData,
      results: {
        passed: passedTests,
        total: totalTests,
        success: this.testResults.overallSuccess,
        details: this.testResults
      },
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        architecture: process.arch
      }
    };

    // Display report
    console.log(`\n${COLORS.PRODUCTION}═══════════════════════════════════════════════════════════════${COLORS.RESET}`);
    console.log(`${COLORS.PRODUCTION}                    PRODUCTION TEST REPORT                      ${COLORS.RESET}`);
    console.log(`${COLORS.PRODUCTION}═══════════════════════════════════════════════════════════════${COLORS.RESET}`);

    console.log(`${COLORS.INFO}📋 Test Suite: ${report.testSuite}${COLORS.RESET}`);
    console.log(`${COLORS.INFO}⏰ Duration: ${report.duration}${COLORS.RESET}`);
    console.log(`${COLORS.INFO}📊 Results: ${report.results.passed}/${report.results.total} tests passed${COLORS.RESET}`);

    const statusColor = report.results.success ? COLORS.SUCCESS : COLORS.ERROR;
    const statusIcon = report.results.success ? '✅' : '❌';
    console.log(`${statusColor}${statusIcon} Overall Status: ${report.results.success ? 'PASSED' : 'FAILED'}${COLORS.RESET}`);

    console.log(`\n${COLORS.INFO}📝 Test Details:${COLORS.RESET}`);

    const testNames = {
      subtitleExtraction: 'Subtitle Extraction Simulation',
      sceneDetection: 'Scene Detection & Context Analysis',
      translation: 'Translation with All Tools',
      secondLanguageValidation: 'Second Language Validation',
      examplesReference: 'Examples Reference System',
      metadataPersistence: 'Metadata Persistence System'
    };

    for (const [key, name] of Object.entries(testNames)) {
      const result = this.testResults[key];
      const icon = result ? '✅' : '❌';
      const color = result ? COLORS.SUCCESS : COLORS.ERROR;
      console.log(`  ${color}${icon} ${name}${COLORS.RESET}`);
    }

    console.log(`\n${COLORS.INFO}🎯 Test Configuration:${COLORS.RESET}`);
    console.log(`  - Anime: ${this.testData.animeTitle}`);
    console.log(`  - Episode: ${this.testData.episode}`);
    console.log(`  - Lines tested: ${this.testData.sampleLines}`);
    console.log(`  - File: ${this.testData.fileName}`);

    console.log(`\n${COLORS.INFO}🖥️  Environment:${COLORS.RESET}`);
    console.log(`  - Node.js: ${report.environment.nodeVersion}`);
    console.log(`  - Platform: ${report.environment.platform}`);
    console.log(`  - Architecture: ${report.environment.architecture}`);

    // Save report to file
    const reportPath = `app/production-test-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n${COLORS.INFO}💾 Report saved to: ${reportPath}${COLORS.RESET}`);

    console.log(`${COLORS.PRODUCTION}═══════════════════════════════════════════════════════════════${COLORS.RESET}\n`);

    if (report.results.success) {
      console.log(`${COLORS.SUCCESS}🎉 [Production Test] All systems operational! Ready for production use.${COLORS.RESET}`);
    } else {
      console.log(`${COLORS.ERROR}⚠️  [Production Test] Some systems failed. Review the report before production deployment.${COLORS.RESET}`);
    }
  }

  /**
   * Cleanup test environment
   */
  async cleanup() {
    console.log(`${COLORS.INFO}🧹 [Production Test] Cleaning up test environment...${COLORS.RESET}`);

    try {
      // Remove test files
      const testFiles = [
        `app/2translate/toTranslate/${this.testData.fileName}`,
        `app/2translate/toTranslate/${this.testData.fileName.replace('_eng.txt', '_other.txt')}`
      ];

      for (const file of testFiles) {
        if (fs.existsSync(file)) {
          fs.unlinkSync(file);
        }
      }

      console.log(`${COLORS.SUCCESS}✅ [Production Test] Cleanup completed${COLORS.RESET}`);

    } catch (error) {
      console.warn(`${COLORS.WARNING}⚠️  [Production Test] Cleanup warning: ${error.message}${COLORS.RESET}`);
    }
  }
}

// Main execution
async function main() {
  const productionTest = new ProductionTest();
  await productionTest.runProductionTest();

  // Exit with appropriate code
  const success = productionTest.testResults.overallSuccess;
  process.exit(success ? 0 : 1);
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error(`${COLORS.ERROR}💥 [Production Test] Fatal error: ${error.message}${COLORS.RESET}`);
    console.error(error.stack);
    process.exit(1);
  });
}

export { ProductionTest };
