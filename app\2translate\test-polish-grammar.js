// @ts-check

/**
 * Test Polish Grammar Checking
 * 
 * This script tests the enhanced Polish grammar checking capabilities
 * with various common errors and punctuation mistakes.
 */

import { config } from 'dotenv';
config();

import { Claude4Translator } from './claude4-translator.js';

console.log('🧪 Testing Enhanced Polish Grammar Checking...\n');

// Test cases with English input that should trigger Polish grammar checking
const testCases = [
  {
    name: "Punctuation Errors (High Priority)",
    text: `akira: I think that this is a good idea.
yuki: I want everything to be ready.
sensei: Please, everyone be quiet.
akira: I can't because I'm busy.
yuki: Yes but I don't know if it's possible.
narrator: He went, and came back quickly.`
  },
  {
    name: "Honorific Usage",
    text: `akira: <PERSON><PERSON>-chan, are you ready?
yuki: Yes, Akira-kun!
sensei: <PERSON><PERSON>sa<PERSON>, please come to the board.`
  },
  {
    name: "Common Grammar Patterns",
    text: `akira: Let's meet in the morning.
yuki: Do you know if he will come?
sensei: You must wash your hands before eating.
akira: I have 3 cats at home.`
  },
  {
    name: "Complex Expressions",
    text: `akira: I don't understand at all.
yuki: For now I'll stay at home.
sensei: That's impossible!
akira: That doesn't make sense.
yuki: I feel comfortable here.`
  },
  {
    name: "Emphasis and Redundancy",
    text: `akira: I have very much work.
yuki: I am completely totally tired.`
  },
  {
    name: "Time and Date References",
    text: `akira: Let's meet on Monday.
yuki: February is a cold month.`
  }
];

async function testGrammarChecking() {
  try {
    const translator = new Claude4Translator({
      enableScreenshots: false,
      enableCorrection: true, // Enable to see grammar checking in action
      maxRetries: 1
    });

    for (const testCase of testCases) {
      console.log(`\n🔍 Testing: ${testCase.name}`);
      console.log(`📝 Input text:`);
      console.log(`${testCase.text}\n`);

      try {
        const result = await translator.translateSubtitles(
          testCase.text,
          null,
          { title: 'Grammar Test', characters: [{ name: 'akira' }, { name: 'yuki' }, { name: 'sensei' }] }
        );

        console.log(`✅ Translation completed`);
        console.log(`📄 Result:`);
        console.log(`${result}\n`);
        
      } catch (error) {
        console.error(`❌ Test failed for "${testCase.name}": ${error.message}\n`);
      }

      // Add a small delay between tests
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    console.log('\n🎉 Grammar checking tests completed!');
    console.log('\n📊 Summary of Enhanced Grammar Checks:');
    console.log('✅ Punctuation errors (missing commas before że, żeby, aby, bo, ale)');
    console.log('✅ Incorrect commas (before "i", "oraz")');
    console.log('✅ Japanese honorific inflection errors');
    console.log('✅ Common Polish grammar mistakes');
    console.log('✅ Preposition errors (na vs w with time)');
    console.log('✅ Reflexive verb errors (missing "się")');
    console.log('✅ Number case agreement errors');
    console.log('✅ Spelling errors (wogóle, narazie)');
    console.log('✅ English calques (robić sens, czuć się komfortowo)');
    console.log('✅ Redundant expressions');
    console.log('✅ Capitalization errors (months, days)');

  } catch (error) {
    console.error('❌ Grammar testing failed:', error.message);
  }
}

// Test just the grammar checking function directly on Polish text with errors
async function testGrammarFunction() {
  console.log('\n🔧 Testing Grammar Function Directly on Polish Text with Common Errors...\n');

  const translator = new Claude4Translator({
    enableScreenshots: false,
    enableCorrection: false
  });

  // Polish text with intentional grammar errors to test detection
  const testText = `akira: Myślę że to dobry pomysł, i pójdę tam.
yuki: Chcę żeby Yuki-chanie była szczęśliwa.
sensei: Wogóle nie rozumiem tego narazie.
narrator: To nie robi sens, ale czuję się komfortowo.
teacher: Spotkajmy się na rano w Poniedziałek.`;

  console.log('📝 Testing Polish text with intentional errors:');
  console.log(testText);
  console.log();

  const issues = translator.correctionTool.checkPolishGrammar(testText);

  console.log(`🔍 Found ${issues.length} grammar issues:`);
  issues.forEach((issue, i) => {
    const severityColor = issue.severity === 'high' ? '🔴' :
                         issue.severity === 'medium' ? '🟡' : '🟢';
    console.log(`${severityColor} ${i + 1}. Line ${issue.line_number}: ${issue.description}`);
    console.log(`   💡 Suggestion: ${issue.suggestion}\n`);
  });
}

// Run tests
async function runAllTests() {
  await testGrammarFunction();
  
  if (process.env.ANTHROPIC_API_KEY) {
    await testGrammarChecking();
  } else {
    console.log('\n⏭️ Skipping full translation tests (no API key)');
    console.log('💡 Set ANTHROPIC_API_KEY to test full translation with grammar checking');
  }
}

runAllTests().catch(console.error);
