/**
 * Context Manager
 *
 * Manages translation context across scenes and episodes to ensure consistency.
 * Tracks character information, terminology, and translation patterns to maintain
 * coherent translations throughout the entire episode.
 */

// Color constants for better log readability
const COLORS = {
  RESET: '\x1b[0m',
  GREEN: '\x1b[32m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  GRAY: '\x1b[90m',
  SUCCESS: '\x1b[32m\x1b[1m',
  INFO: '\x1b[36m',
  DEBUG: '\x1b[90m',
  CONTEXT: '\x1b[35m\x1b[1m'
};

export class ContextManager {
  constructor() {
    this.reset();
    console.log(`${COLORS.SUCCESS}🧠 [ContextManager] Initialized${COLORS.RESET}`);
  }

  /**
   * Reset context for new episode
   */
  reset() {
    this.animeMetadata = {};
    this.characterInfo = new Map();
    this.terminology = new Map();
    this.sceneHistory = [];
    this.translationPatterns = new Map();
    this.currentEpisodeContext = {
      title: '',
      episode: '',
      totalScenes: 0,
      processedScenes: 0
    };
  }

  /**
   * Initialize context with anime metadata
   * @param {Object} metadata - Anime metadata
   */
  initialize(metadata = {}) {
    this.animeMetadata = {
      title: metadata.title || '',
      characters: metadata.characters || [],
      genres: metadata.genres || [],
      episode: metadata.episode || '',
      ...metadata
    };

    // Initialize character information
    if (metadata.characters) {
      metadata.characters.forEach(char => {
        this.characterInfo.set(char.name?.toLowerCase() || char, {
          name: char.name || char,
          gender: char.gender || 'unknown',
          honorifics: char.honorifics || [],
          speechPattern: char.speechPattern || 'normal',
          translations: new Map()
        });
      });
    }

    console.log(`${COLORS.CONTEXT}📺 [ContextManager] Initialized for ${this.animeMetadata.title} - Episode ${this.animeMetadata.episode}${COLORS.RESET}`);
  }

  /**
   * Update context with scene analysis
   * @param {Object} analysis - Scene analysis from Claude
   */
  updateSceneAnalysis(analysis) {
    if (analysis.speakers) {
      analysis.speakers.forEach(speaker => {
        if (!this.characterInfo.has(speaker.toLowerCase())) {
          this.characterInfo.set(speaker.toLowerCase(), {
            name: speaker,
            gender: 'unknown',
            honorifics: [],
            speechPattern: 'normal',
            translations: new Map()
          });
        }
      });
    }

    if (analysis.cultural_references) {
      analysis.cultural_references.forEach(ref => {
        if (!this.terminology.has(ref.toLowerCase())) {
          this.terminology.set(ref.toLowerCase(), {
            original: ref,
            translations: [],
            context: 'cultural_reference'
          });
        }
      });
    }
  }

  /**
   * Update context after scene translation
   * @param {Object} scene - Original scene
   * @param {string} translation - Translated content
   */
  updateContext(scene, translation) {
    // Store scene in history
    this.sceneHistory.push({
      index: scene.index,
      speakers: scene.speakers,
      originalContent: scene.content,
      translatedContent: translation,
      timestamp: scene.timestamp,
      emotionalTone: scene.emotionalTone
    });

    // Update character translation patterns
    this.updateCharacterTranslations(scene, translation);

    // Update terminology
    this.updateTerminology(scene.content, translation);

    // Update episode progress
    this.currentEpisodeContext.processedScenes++;

    console.log(`${COLORS.DEBUG}📝 [ContextManager] Updated context for scene ${scene.index + 1}${COLORS.RESET}`);
  }

  /**
   * Update character-specific translation patterns
   * @param {Object} scene - Original scene
   * @param {string} translation - Translated content
   */
  updateCharacterTranslations(scene, translation) {
    const originalLines = scene.content.split('\n');
    const translatedLines = translation.split('\n');

    for (let i = 0; i < Math.min(originalLines.length, translatedLines.length); i++) {
      const originalLine = originalLines[i];
      const translatedLine = translatedLines[i];

      const colonIndex = originalLine.indexOf(':');
      if (colonIndex === -1) continue;

      const speaker = originalLine.slice(0, colonIndex).trim().toLowerCase();
      const originalDialogue = originalLine.slice(colonIndex + 1).trim();
      const translatedDialogue = translatedLine.slice(translatedLine.indexOf(':') + 1).trim();

      if (this.characterInfo.has(speaker)) {
        const charInfo = this.characterInfo.get(speaker);
        charInfo.translations.set(originalDialogue, translatedDialogue);
        
        // Analyze speech patterns
        this.analyzeCharacterSpeechPattern(speaker, translatedDialogue);
      }
    }
  }

  /**
   * Analyze and update character speech patterns
   * @param {string} speaker - Character name
   * @param {string} dialogue - Translated dialogue
   */
  analyzeCharacterSpeechPattern(speaker, dialogue) {
    const charInfo = this.characterInfo.get(speaker);
    if (!charInfo) return;

    // Analyze formality level
    const formalIndicators = ['pan', 'pani', 'proszę', 'dziękuję'];
    const informalIndicators = ['hej', 'cześć', 'spoko', 'ziom'];
    //TODO: Maybe add more indicators?

    const formalCount = formalIndicators.filter(word => 
      dialogue.toLowerCase().includes(word)
    ).length;
    
    const informalCount = informalIndicators.filter(word => 
      dialogue.toLowerCase().includes(word)
    ).length;

    if (formalCount > informalCount) {
      charInfo.speechPattern = 'formal';
    } else if (informalCount > formalCount) {
      charInfo.speechPattern = 'informal';
    }

    // Detect honorific usage
    const honorifics = ['-san', '-chan', '-kun', '-sama', '-senpai'];
    honorifics.forEach(honorific => {
      if (dialogue.includes(honorific) && !charInfo.honorifics.includes(honorific)) {
        charInfo.honorifics.push(honorific);
      }
    });
  }

  /**
   * Update terminology database
   * @param {string} original - Original text
   * @param {string} translation - Translated text
   */
  updateTerminology(original, translation) {
    // Extract potential terminology pairs
    // This is a simplified approach - could be enhanced with NLP
    // TODO: enhance with NLP
    const originalWords = original.toLowerCase().split(/\s+/);
    const translatedWords = translation.toLowerCase().split(/\s+/);

    // Look for consistent translations of specific terms
    const specialTerms = ['school', 'club', 'festival', 'senpai', 'kohai', 'sensei'];
    
    specialTerms.forEach(term => {
      if (originalWords.includes(term)) {
        // Find corresponding translation (simplified)
        const termIndex = originalWords.indexOf(term);
        if (termIndex < translatedWords.length) {
          const translatedTerm = translatedWords[termIndex];
          
          if (!this.terminology.has(term)) {
            this.terminology.set(term, {
              original: term,
              translations: [],
              context: 'general'
            });
          }
          
          const termInfo = this.terminology.get(term);
          if (!termInfo.translations.includes(translatedTerm)) {
            termInfo.translations.push(translatedTerm);
          }
        }
      }
    });
  }

  /**
   * Get current context summary for translation
   * @returns {string} - Context summary
   */
  getContextSummary() {
    const characterList = Array.from(this.characterInfo.entries())
      .map(([name, info]) => `${info.name} (${info.gender}, ${info.speechPattern})`)
      .join(', ');

    const recentScenes = this.sceneHistory.slice(-3)
      .map(scene => `Scene ${scene.index + 1}: ${scene.emotionalTone}`)
      .join(', ');

    return `Episode: ${this.animeMetadata.title} - ${this.animeMetadata.episode}
Characters: ${characterList || 'None identified'}
Recent scenes: ${recentScenes || 'None'}
Progress: ${this.currentEpisodeContext.processedScenes}/${this.currentEpisodeContext.totalScenes || '?'} scenes`;
  }

  /**
   * Get full context for tools
   * @returns {Object} - Complete context object
   */
  getContext() {
    return {
      anime: this.animeMetadata,
      characters: Array.from(this.characterInfo.values()),
      terminology: Array.from(this.terminology.values()),
      recentScenes: this.sceneHistory.slice(-5),
      episodeProgress: this.currentEpisodeContext
    };
  }

  /**
   * Get character-specific context
   * @param {string} characterName - Character name
   * @returns {Object|null} - Character context
   */
  getCharacterContext(characterName) {
    const normalizedName = characterName.toLowerCase();
    return this.characterInfo.get(normalizedName) || null;
  }

  /**
   * Get translation for specific term if available
   * @param {string} term - Term to look up
   * @returns {string|null} - Preferred translation or null
   */
  getTermTranslation(term) {
    const termInfo = this.terminology.get(term.toLowerCase());
    if (termInfo && termInfo.translations.length > 0) {
      // Return most frequently used translation
      return termInfo.translations[0];
    }
    return null;
  }

  /**
   * Check if character has been seen before
   * @param {string} characterName - Character name
   * @returns {boolean} - True if character is known
   */
  isKnownCharacter(characterName) {
    return this.characterInfo.has(characterName.toLowerCase());
  }

  /**
   * Get character speech pattern
   * @param {string} characterName - Character name
   * @returns {string} - Speech pattern (formal/informal/normal)
   */
  getCharacterSpeechPattern(characterName) {
    const charInfo = this.characterInfo.get(characterName.toLowerCase());
    return charInfo ? charInfo.speechPattern : 'normal';
  }

  /**
   * Get character gender for pronoun usage
   * @param {string} characterName - Character name
   * @returns {string} - Gender (male/female/unknown)
   */
  getCharacterGender(characterName) {
    const charInfo = this.characterInfo.get(characterName.toLowerCase());
    return charInfo ? charInfo.gender : 'unknown';
  }

  /**
   * Add custom terminology
   * @param {string} original - Original term
   * @param {string} translation - Polish translation
   * @param {string} context - Context type
   */
  addTerminology(original, translation, context = 'custom') {
    if (!this.terminology.has(original.toLowerCase())) {
      this.terminology.set(original.toLowerCase(), {
        original: original,
        translations: [translation],
        context: context
      });
    } else {
      const termInfo = this.terminology.get(original.toLowerCase());
      if (!termInfo.translations.includes(translation)) {
        termInfo.translations.push(translation);
      }
    }
  }

  /**
   * Set total scene count for progress tracking
   * @param {number} count - Total number of scenes
   */
  setTotalScenes(count) {
    this.currentEpisodeContext.totalScenes = count;
  }

  /**
   * Get translation statistics
   * @returns {Object} - Translation statistics
   */
  getStatistics() {
    return {
      totalScenes: this.sceneHistory.length,
      uniqueCharacters: this.characterInfo.size,
      terminologyEntries: this.terminology.size,
      averageSceneLength: this.sceneHistory.length > 0 
        ? this.sceneHistory.reduce((sum, scene) => sum + scene.originalContent.split('\n').length, 0) / this.sceneHistory.length
        : 0
    };
  }
}
