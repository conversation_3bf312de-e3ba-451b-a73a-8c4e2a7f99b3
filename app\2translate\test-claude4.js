// @ts-check

/**
 * Test script for Claude 4 Translation System
 *
 * This script tests the new Claude 4 translation system with sample data
 * to verify all components work correctly.
 */

import { config } from 'dotenv';
config();

import { Claude4Translator } from './claude4-translator.js';
import fs from 'fs';
import path from 'path';

// Test data
const sampleSubtitles = `narrator: In a world where magic and technology coexist...
akira: Hey, <PERSON><PERSON>-chan! Are you ready for the school festival?
yuki: Yes, Akira-kun! I've been preparing for weeks.
akira: That's great! What's your club doing?
yuki: We're doing a maid café. It's going to be so much fun!
akira: A maid café? That sounds interesting.
yuki: You should come visit us! I'll be wearing a cute maid outfit.
akira: I definitely will! When does it start?
yuki: Tomorrow at 10 AM. Don't be late!
akira: I won't! See you tomorrow, <PERSON><PERSON>-chan.
narrator: And so, the preparations for the festival continued...`;

const sampleMetadata = {
  title: 'Test Anime',
  episode: '01',
  characters: [
    { name: 'akira', gender: 'male' },
    { name: 'yuki', gender: 'female' },
    { name: 'narrator', gender: 'unknown' }
  ],
  genres: ['school', 'comedy', 'slice of life']
};

async function testClaude4Translation() {
  console.log('🧪 Testing Claude 4 Translation System...\n');

  try {
    // Test 1: Basic initialization
    console.log('1️⃣ Testing initialization...');
    const translator = new Claude4Translator({
      maxTokens: 4096,
      temperature: 0.7,
      enableScreenshots: false, // Disable for testing
      enableCorrection: true,
      maxRetries: 2
    });
    console.log('✅ Translator initialized successfully\n');

    // Test 2: Scene detection
    console.log('2️⃣ Testing scene detection...');
    const scenes = await translator.sceneDetector.detectScenes(sampleSubtitles);
    console.log(`✅ Detected ${scenes.length} scenes:`);
    scenes.forEach((scene, i) => {
      console.log(`   Scene ${i + 1}: ${scene.lines.length} lines, speakers: ${scene.speakers.join(', ')}`);
    });
    console.log();

    // Test 3: Context management
    console.log('3️⃣ Testing context management...');
    translator.contextManager.initialize(sampleMetadata);
    console.log('✅ Context initialized with metadata');
    console.log(`   Characters: ${translator.contextManager.characterInfo.size}`);
    console.log(`   Context summary: ${translator.contextManager.getContextSummary()}\n`);

    // Test 4: Translation (if API key available)
    if (process.env.ANTHROPIC_API_KEY) {
      console.log('4️⃣ Testing translation...');
      try {
        const translatedContent = await translator.translateSubtitles(
          sampleSubtitles,
          null, // No video file for testing
          sampleMetadata
        );
        
        console.log('✅ Translation completed successfully');
        console.log('📝 Sample translation:');
        console.log(translatedContent.split('\n').slice(0, 3).join('\n'));
        console.log('   ...\n');

        // Save test output
        const outputPath = path.join('app/2translate', 'test-output.txt');
        fs.writeFileSync(outputPath, translatedContent);
        console.log(`💾 Full translation saved to: ${outputPath}\n`);

      } catch (error) {
        console.log(`❌ Translation test failed: ${error.message}\n`);
      }
    } else {
      console.log('4️⃣ Skipping translation test (no API key)\n');
    }

    // Test 5: Screenshot tool (basic functionality)
    console.log('5️⃣ Testing screenshot tool...');
    try {
      const screenshotTool = translator.screenshotTool;
      console.log('✅ Screenshot tool initialized');
      console.log(`   Output directory: ${screenshotTool.outputDir}`);
      console.log(`   Quality setting: ${screenshotTool.quality}\n`);
    } catch (error) {
      console.log(`❌ Screenshot tool test failed: ${error.message}\n`);
    }

    // Test 6: Correction tool
    console.log('6️⃣ Testing correction tool...');
    try {
      const correctionTool = translator.correctionTool;
      console.log('✅ Correction tool initialized');
      console.log(`   Max iterations: ${correctionTool.maxIterations}`);
      console.log(`   Quality threshold: ${correctionTool.qualityThreshold}\n`);
    } catch (error) {
      console.log(`❌ Correction tool test failed: ${error.message}\n`);
    }

    console.log('🎉 All tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('   ✅ Initialization: PASSED');
    console.log('   ✅ Scene Detection: PASSED');
    console.log('   ✅ Context Management: PASSED');
    console.log(`   ${process.env.ANTHROPIC_API_KEY ? '✅' : '⏭️'} Translation: ${process.env.ANTHROPIC_API_KEY ? 'PASSED' : 'SKIPPED'}`);
    console.log('   ✅ Screenshot Tool: PASSED');
    console.log('   ✅ Correction Tool: PASSED');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error(error.stack);
    process.exit(1);
  }
}

async function testSceneDetectionDetails() {
  console.log('\n🔍 Detailed Scene Detection Analysis...\n');

  const translator = new Claude4Translator({ enableScreenshots: false });
  const scenes = await translator.sceneDetector.detectScenes(sampleSubtitles);

  scenes.forEach((scene, i) => {
    console.log(`📋 Scene ${i + 1} Details:`);
    console.log(`   Lines: ${scene.lines.length}`);
    console.log(`   Speakers: ${scene.speakers.join(', ')}`);
    console.log(`   Word count: ${scene.wordCount}`);
    console.log(`   Emotional tone: ${scene.emotionalTone}`);
    console.log(`   Has action: ${scene.hasAction}`);
    console.log(`   Content preview: "${scene.content.split('\n')[0]}..."`);
    console.log();
  });
}

async function testContextPersistence() {
  console.log('\n💾 Testing Context Persistence...\n');

  const translator = new Claude4Translator({ enableScreenshots: false });
  translator.contextManager.initialize(sampleMetadata);

  // Simulate processing scenes
  const scenes = await translator.sceneDetector.detectScenes(sampleSubtitles);
  
  for (let i = 0; i < scenes.length; i++) {
    const scene = scenes[i];
    const mockTranslation = scene.content.replace(/:/g, ' |'); // Mock translation
    
    translator.contextManager.updateContext(scene, mockTranslation);
    
    console.log(`📈 After scene ${i + 1}:`);
    console.log(`   Processed scenes: ${translator.contextManager.currentEpisodeContext.processedScenes}`);
    console.log(`   Known characters: ${translator.contextManager.characterInfo.size}`);
    console.log(`   Scene history: ${translator.contextManager.sceneHistory.length}`);
  }

  const stats = translator.contextManager.getStatistics();
  console.log('\n📊 Final Statistics:');
  console.log(`   Total scenes: ${stats.totalScenes}`);
  console.log(`   Unique characters: ${stats.uniqueCharacters}`);
  console.log(`   Terminology entries: ${stats.terminologyEntries}`);
  console.log(`   Average scene length: ${stats.averageSceneLength.toFixed(1)} lines`);
}

// Run tests
async function runAllTests() {
  await testClaude4Translation();
  await testSceneDetectionDetails();
  await testContextPersistence();
  
  console.log('\n🏁 All tests completed!');
}

// Execute if run directly
runAllTests().catch(console.error);

export { testClaude4Translation, testSceneDetectionDetails, testContextPersistence };
