// @ts-check

/**
 * Metadata Persistence Tool
 * 
 * This tool manages anime metadata persistence across episodes, ensuring
 * consistency in character information, terminology, and translation patterns
 * throughout the entire series.
 * 
 * Features:
 * - Persistent storage of anime metadata
 * - Character information tracking across episodes
 * - Translation pattern consistency
 * - Series-wide terminology management
 * - Episode progress tracking
 */

import fs from 'fs';
import path from 'path';

// Color constants for better log readability
const COLORS = {
  RESET: '\x1b[0m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  RED: '\x1b[31m',
  GRAY: '\x1b[90m',
  SUCCESS: '\x1b[32m\x1b[1m',
  WARNING: '\x1b[33m\x1b[1m',
  ERROR: '\x1b[31m\x1b[1m',
  INFO: '\x1b[36m',
  DEBUG: '\x1b[90m',
  METADATA: '\x1b[36m\x1b[1m'
};

export class MetadataPersistence {
  constructor(options = {}) {
    this.metadataDirectory = options.metadataDirectory || 'app/2translate/metadata';
    this.enablePersistence = options.enablePersistence !== false;
    this.autoSave = options.autoSave !== false;
    this.saveInterval = options.saveInterval || 30000; // 30 seconds
    
    // In-memory cache
    this.animeMetadata = new Map();
    this.characterDatabase = new Map();
    this.terminologyDatabase = new Map();
    this.translationPatterns = new Map();
    
    // Auto-save timer
    this.saveTimer = null;
    
    this.ensureDirectories();
    this.loadPersistedData();
    
    if (this.autoSave) {
      this.startAutoSave();
    }
    
    console.log(`${COLORS.SUCCESS}💾 [MetadataPersistence] Initialized with persistence enabled: ${this.enablePersistence}${COLORS.RESET}`);
  }

  /**
   * Ensure metadata directories exist
   */
  ensureDirectories() {
    if (!this.enablePersistence) return;
    
    try {
      if (!fs.existsSync(this.metadataDirectory)) {
        fs.mkdirSync(this.metadataDirectory, { recursive: true });
      }
      
      const subdirs = ['anime', 'characters', 'terminology', 'patterns'];
      subdirs.forEach(subdir => {
        const dirPath = path.join(this.metadataDirectory, subdir);
        if (!fs.existsSync(dirPath)) {
          fs.mkdirSync(dirPath, { recursive: true });
        }
      });
      
    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [MetadataPersistence] Failed to create directories: ${error.message}${COLORS.RESET}`);
    }
  }

  /**
   * Load persisted data from disk
   */
  async loadPersistedData() {
    if (!this.enablePersistence) return;
    
    try {
      console.log(`${COLORS.METADATA}📂 [MetadataPersistence] Loading persisted data...${COLORS.RESET}`);
      
      // Load anime metadata
      await this.loadAnimeMetadata();
      
      // Load character database
      await this.loadCharacterDatabase();
      
      // Load terminology database
      await this.loadTerminologyDatabase();
      
      // Load translation patterns
      await this.loadTranslationPatterns();
      
      console.log(`${COLORS.SUCCESS}✅ [MetadataPersistence] Loaded persisted data successfully${COLORS.RESET}`);
      
    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [MetadataPersistence] Failed to load persisted data: ${error.message}${COLORS.RESET}`);
    }
  }

  /**
   * Load anime metadata from disk
   */
  async loadAnimeMetadata() {
    const animeDir = path.join(this.metadataDirectory, 'anime');
    if (!fs.existsSync(animeDir)) return;
    
    const files = fs.readdirSync(animeDir).filter(file => file.endsWith('.json'));
    
    for (const file of files) {
      try {
        const filePath = path.join(animeDir, file);
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        const animeTitle = path.basename(file, '.json');
        this.animeMetadata.set(animeTitle, data);
      } catch (error) {
        console.warn(`${COLORS.WARNING}⚠️  [MetadataPersistence] Failed to load anime metadata from ${file}: ${error.message}${COLORS.RESET}`);
      }
    }
    
    console.log(`${COLORS.INFO}📺 [MetadataPersistence] Loaded ${this.animeMetadata.size} anime metadata entries${COLORS.RESET}`);
  }

  /**
   * Load character database from disk
   */
  async loadCharacterDatabase() {
    const charactersDir = path.join(this.metadataDirectory, 'characters');
    if (!fs.existsSync(charactersDir)) return;
    
    const files = fs.readdirSync(charactersDir).filter(file => file.endsWith('.json'));
    
    for (const file of files) {
      try {
        const filePath = path.join(charactersDir, file);
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        const animeTitle = path.basename(file, '.json');
        this.characterDatabase.set(animeTitle, data);
      } catch (error) {
        console.warn(`${COLORS.WARNING}⚠️  [MetadataPersistence] Failed to load character data from ${file}: ${error.message}${COLORS.RESET}`);
      }
    }
    
    console.log(`${COLORS.INFO}👥 [MetadataPersistence] Loaded character data for ${this.characterDatabase.size} anime${COLORS.RESET}`);
  }

  /**
   * Load terminology database from disk
   */
  async loadTerminologyDatabase() {
    const terminologyDir = path.join(this.metadataDirectory, 'terminology');
    if (!fs.existsSync(terminologyDir)) return;
    
    const files = fs.readdirSync(terminologyDir).filter(file => file.endsWith('.json'));
    
    for (const file of files) {
      try {
        const filePath = path.join(terminologyDir, file);
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        const animeTitle = path.basename(file, '.json');
        this.terminologyDatabase.set(animeTitle, data);
      } catch (error) {
        console.warn(`${COLORS.WARNING}⚠️  [MetadataPersistence] Failed to load terminology from ${file}: ${error.message}${COLORS.RESET}`);
      }
    }
    
    console.log(`${COLORS.INFO}📚 [MetadataPersistence] Loaded terminology for ${this.terminologyDatabase.size} anime${COLORS.RESET}`);
  }

  /**
   * Load translation patterns from disk
   */
  async loadTranslationPatterns() {
    const patternsDir = path.join(this.metadataDirectory, 'patterns');
    if (!fs.existsSync(patternsDir)) return;
    
    const files = fs.readdirSync(patternsDir).filter(file => file.endsWith('.json'));
    
    for (const file of files) {
      try {
        const filePath = path.join(patternsDir, file);
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        const animeTitle = path.basename(file, '.json');
        this.translationPatterns.set(animeTitle, data);
      } catch (error) {
        console.warn(`${COLORS.WARNING}⚠️  [MetadataPersistence] Failed to load patterns from ${file}: ${error.message}${COLORS.RESET}`);
      }
    }
    
    console.log(`${COLORS.INFO}🔄 [MetadataPersistence] Loaded translation patterns for ${this.translationPatterns.size} anime${COLORS.RESET}`);
  }

  /**
   * Get or create anime metadata entry
   * @param {string} animeTitle - Anime title
   * @param {Object} initialData - Initial metadata if creating new entry
   * @returns {Object} - Anime metadata
   */
  getAnimeMetadata(animeTitle, initialData = {}) {
    const normalizedTitle = this.normalizeTitle(animeTitle);
    
    if (!this.animeMetadata.has(normalizedTitle)) {
      const metadata = {
        title: animeTitle,
        normalizedTitle: normalizedTitle,
        episodes: {},
        characters: [],
        genres: [],
        totalEpisodes: 0,
        lastUpdated: new Date().toISOString(),
        translationStats: {
          totalScenes: 0,
          totalLines: 0,
          averageQuality: 0
        },
        ...initialData
      };
      
      this.animeMetadata.set(normalizedTitle, metadata);
      console.log(`${COLORS.INFO}📺 [MetadataPersistence] Created new anime metadata for: ${animeTitle}${COLORS.RESET}`);
    }
    
    return this.animeMetadata.get(normalizedTitle);
  }

  /**
   * Update anime metadata
   * @param {string} animeTitle - Anime title
   * @param {Object} updates - Updates to apply
   */
  updateAnimeMetadata(animeTitle, updates) {
    const normalizedTitle = this.normalizeTitle(animeTitle);
    const metadata = this.getAnimeMetadata(animeTitle);
    
    Object.assign(metadata, updates, {
      lastUpdated: new Date().toISOString()
    });
    
    this.animeMetadata.set(normalizedTitle, metadata);
    
    if (this.enablePersistence) {
      this.scheduleAutoSave();
    }
  }

  /**
   * Add episode data to anime metadata
   * @param {string} animeTitle - Anime title
   * @param {string} episodeNumber - Episode number
   * @param {Object} episodeData - Episode data
   */
  addEpisodeData(animeTitle, episodeNumber, episodeData) {
    const metadata = this.getAnimeMetadata(animeTitle);
    
    metadata.episodes[episodeNumber] = {
      number: episodeNumber,
      translatedAt: new Date().toISOString(),
      scenes: episodeData.scenes || 0,
      lines: episodeData.lines || 0,
      quality: episodeData.quality || 0,
      characters: episodeData.characters || [],
      ...episodeData
    };
    
    // Update total episodes count
    metadata.totalEpisodes = Object.keys(metadata.episodes).length;
    
    // Update translation stats
    const episodes = Object.values(metadata.episodes);
    metadata.translationStats = {
      totalScenes: episodes.reduce((sum, ep) => sum + (ep.scenes || 0), 0),
      totalLines: episodes.reduce((sum, ep) => sum + (ep.lines || 0), 0),
      averageQuality: episodes.reduce((sum, ep) => sum + (ep.quality || 0), 0) / episodes.length
    };
    
    this.updateAnimeMetadata(animeTitle, metadata);
    
    console.log(`${COLORS.INFO}📺 [MetadataPersistence] Added episode ${episodeNumber} data for ${animeTitle}${COLORS.RESET}`);
  }

  /**
   * Get character information for anime
   * @param {string} animeTitle - Anime title
   * @returns {Object} - Character information
   */
  getCharacterInfo(animeTitle) {
    const normalizedTitle = this.normalizeTitle(animeTitle);
    return this.characterDatabase.get(normalizedTitle) || {};
  }

  /**
   * Update character information
   * @param {string} animeTitle - Anime title
   * @param {Object} characterData - Character data to update
   */
  updateCharacterInfo(animeTitle, characterData) {
    const normalizedTitle = this.normalizeTitle(animeTitle);
    const existing = this.characterDatabase.get(normalizedTitle) || {};

    const updated = {
      ...existing,
      ...characterData,
      lastUpdated: new Date().toISOString()
    };

    this.characterDatabase.set(normalizedTitle, updated);

    if (this.enablePersistence) {
      this.scheduleAutoSave();
    }
  }

  /**
   * Get terminology for anime
   * @param {string} animeTitle - Anime title
   * @returns {Object} - Terminology data
   */
  getTerminology(animeTitle) {
    const normalizedTitle = this.normalizeTitle(animeTitle);
    return this.terminologyDatabase.get(normalizedTitle) || {};
  }

  /**
   * Update terminology
   * @param {string} animeTitle - Anime title
   * @param {Object} terminologyData - Terminology data to update
   */
  updateTerminology(animeTitle, terminologyData) {
    const normalizedTitle = this.normalizeTitle(animeTitle);
    const existing = this.terminologyDatabase.get(normalizedTitle) || {};

    const updated = {
      ...existing,
      ...terminologyData,
      lastUpdated: new Date().toISOString()
    };

    this.terminologyDatabase.set(normalizedTitle, updated);

    if (this.enablePersistence) {
      this.scheduleAutoSave();
    }
  }

  /**
   * Get translation patterns for anime
   * @param {string} animeTitle - Anime title
   * @returns {Object} - Translation patterns
   */
  getTranslationPatterns(animeTitle) {
    const normalizedTitle = this.normalizeTitle(animeTitle);
    return this.translationPatterns.get(normalizedTitle) || {};
  }

  /**
   * Update translation patterns
   * @param {string} animeTitle - Anime title
   * @param {Object} patternsData - Translation patterns to update
   */
  updateTranslationPatterns(animeTitle, patternsData) {
    const normalizedTitle = this.normalizeTitle(animeTitle);
    const existing = this.translationPatterns.get(normalizedTitle) || {};

    const updated = {
      ...existing,
      ...patternsData,
      lastUpdated: new Date().toISOString()
    };

    this.translationPatterns.set(normalizedTitle, updated);

    if (this.enablePersistence) {
      this.scheduleAutoSave();
    }
  }

  /**
   * Save all data to disk
   */
  async saveAllData() {
    if (!this.enablePersistence) return;

    try {
      console.log(`${COLORS.METADATA}💾 [MetadataPersistence] Saving all data to disk...${COLORS.RESET}`);

      // Save anime metadata
      for (const [title, data] of this.animeMetadata) {
        const filePath = path.join(this.metadataDirectory, 'anime', `${title}.json`);
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
      }

      // Save character database
      for (const [title, data] of this.characterDatabase) {
        const filePath = path.join(this.metadataDirectory, 'characters', `${title}.json`);
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
      }

      // Save terminology database
      for (const [title, data] of this.terminologyDatabase) {
        const filePath = path.join(this.metadataDirectory, 'terminology', `${title}.json`);
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
      }

      // Save translation patterns
      for (const [title, data] of this.translationPatterns) {
        const filePath = path.join(this.metadataDirectory, 'patterns', `${title}.json`);
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
      }

      console.log(`${COLORS.SUCCESS}✅ [MetadataPersistence] All data saved successfully${COLORS.RESET}`);

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [MetadataPersistence] Failed to save data: ${error.message}${COLORS.RESET}`);
    }
  }

  /**
   * Start auto-save timer
   */
  startAutoSave() {
    if (this.saveTimer) {
      clearInterval(this.saveTimer);
    }

    this.saveTimer = setInterval(() => {
      this.saveAllData();
    }, this.saveInterval);

    console.log(`${COLORS.INFO}⏰ [MetadataPersistence] Auto-save started (interval: ${this.saveInterval}ms)${COLORS.RESET}`);
  }

  /**
   * Schedule auto-save (debounced)
   */
  scheduleAutoSave() {
    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout);
    }

    this.autoSaveTimeout = setTimeout(() => {
      this.saveAllData();
    }, 5000); // Save after 5 seconds of inactivity
  }

  /**
   * Stop auto-save and save final data
   */
  async shutdown() {
    if (this.saveTimer) {
      clearInterval(this.saveTimer);
    }

    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout);
    }

    await this.saveAllData();
    console.log(`${COLORS.INFO}🔒 [MetadataPersistence] Shutdown complete${COLORS.RESET}`);
  }

  /**
   * Normalize anime title for consistent storage
   * @param {string} title - Original title
   * @returns {string} - Normalized title
   */
  normalizeTitle(title) {
    return title
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, '_')
      .trim();
  }

  /**
   * Get statistics about stored metadata
   * @returns {Object} - Metadata statistics
   */
  getStatistics() {
    return {
      animeCount: this.animeMetadata.size,
      characterDatabaseCount: this.characterDatabase.size,
      terminologyDatabaseCount: this.terminologyDatabase.size,
      translationPatternsCount: this.translationPatterns.size,
      totalEpisodes: Array.from(this.animeMetadata.values())
        .reduce((sum, anime) => sum + anime.totalEpisodes, 0),
      memoryUsage: {
        animeMetadata: JSON.stringify(Array.from(this.animeMetadata.values())).length,
        characterDatabase: JSON.stringify(Array.from(this.characterDatabase.values())).length,
        terminologyDatabase: JSON.stringify(Array.from(this.terminologyDatabase.values())).length,
        translationPatterns: JSON.stringify(Array.from(this.translationPatterns.values())).length
      }
    };
  }

  /**
   * Clear all metadata (use with caution)
   */
  clearAllMetadata() {
    this.animeMetadata.clear();
    this.characterDatabase.clear();
    this.terminologyDatabase.clear();
    this.translationPatterns.clear();

    console.log(`${COLORS.WARNING}🗑️  [MetadataPersistence] All metadata cleared from memory${COLORS.RESET}`);
  }
}
