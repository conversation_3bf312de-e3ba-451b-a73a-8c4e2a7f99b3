# Claude 4 Sonnet Translation System

This directory contains the refactored translation system that utilizes Claude 4 Sonnet with tool use capabilities for intelligent anime subtitle translation.

## Overview

The new translation system provides significant improvements over the previous approach:

### Key Features

1. **Intelligent Scene Detection**: Automatically groups dialogue into logical scenes based on speaker changes, timing gaps, and content transitions
2. **Visual Context Integration**: Captures screenshots from video files to provide visual context for translation decisions
3. **Iterative Correction**: Uses a correction tool to improve translation quality through multiple passes
4. **Context-Aware Translation**: Maintains character consistency and terminology across the entire episode
5. **Tool-Based Architecture**: Leverages Claude 4's tool use capabilities for specialized translation tasks

## Architecture

### Core Components

- **`claude4-translator.js`**: Main translation engine with tool use capabilities
- **`context-manager.js`**: Manages translation context and character consistency
- **`tools/scene-detector.js`**: Intelligent scene boundary detection
- **`tools/screenshot-tool.js`**: Video frame capture for visual context
- **`tools/correction-tool.js`**: Iterative translation improvement

### Translation Pipeline

1. **Initialization**: Load anime metadata and initialize context
2. **Scene Detection**: Analyze subtitle content to identify natural scene boundaries
3. **Visual Context**: Capture screenshots at key timestamps (if video available)
4. **Translation**: Use Claude 4 with tools to translate each scene
5. **Correction**: Apply iterative improvements to enhance quality
6. **Context Update**: Update character and terminology databases

## Configuration

### Environment Variables

```bash
ANTHROPIC_API_KEY=your_claude_api_key_here
```

### Translation Options

The Claude 4 translator can be configured with various options:

```javascript
const claude4Translator = new Claude4Translator({
  maxTokens: 8192,           // Maximum tokens per request
  temperature: 0.7,          // Translation creativity (0-1)
  enableScreenshots: true,   // Enable visual context
  enableCorrection: true,    // Enable iterative improvement
  maxRetries: 3             // Maximum retry attempts
});
```

### Scene Detection Settings

```javascript
const sceneDetector = new SceneDetector({
  minSceneLength: 5,         // Minimum lines per scene
  maxSceneLength: 80,        // Maximum lines per scene
  timingGapThreshold: 5000,  // Timing gap threshold (ms)
  speakerChangeWeight: 0.3,  // Weight for speaker changes
  timingWeight: 0.4,         // Weight for timing gaps
  contentWeight: 0.3         // Weight for content transitions
});
```

## Usage

### Basic Translation

```javascript
import { Claude4Translator } from './claude4-translator.js';

const translator = new Claude4Translator();

const translatedContent = await translator.translateSubtitles(
  subtitleContent,  // Raw subtitle text
  videoPath,        // Path to video file (optional)
  metadata          // Anime metadata
);
```

### With Custom Configuration

```javascript
const translator = new Claude4Translator({
  enableScreenshots: false,  // Disable screenshots
  enableCorrection: true,    // Enable correction
  maxRetries: 5             // Increase retry attempts
});
```

## Tools

### Scene Detector

Analyzes subtitle content to identify natural scene boundaries:

- **Speaker Analysis**: Detects conversation patterns and speaker changes
- **Timing Analysis**: Identifies gaps in dialogue timing
- **Content Analysis**: Analyzes vocabulary overlap and topic shifts
- **Emotional Tone**: Detects shifts in emotional context

### Screenshot Tool

Captures video frames for visual context:

- **Frame Extraction**: Uses FFmpeg to extract frames at specific timestamps
- **Image Analysis**: Analyzes brightness, aspect ratio, and visual elements
- **Subtitle Detection**: Identifies presence of subtitle overlays
- **Cleanup**: Automatically removes old screenshots

### Correction Tool

Provides iterative translation improvement:

- **Quality Analysis**: Evaluates grammar, naturalness, accuracy, and consistency
- **Issue Detection**: Identifies specific problems in translations
- **Improvement Generation**: Creates enhanced versions addressing issues
- **Polish Language Rules**: Applies Polish-specific grammar and style rules

### Context Manager

Maintains translation consistency:

- **Character Tracking**: Monitors character speech patterns and relationships
- **Terminology Database**: Maintains consistent translations of key terms
- **Scene History**: Tracks previous translations for context
- **Progress Monitoring**: Provides translation statistics and progress

## File Structure

```
app/2translate/
├── claude4-translator.js     # Main translation engine
├── context-manager.js        # Context management
├── translate.js             # Updated main script
├── tools/
│   ├── scene-detector.js    # Scene boundary detection
│   ├── screenshot-tool.js   # Video frame capture
│   └── correction-tool.js   # Translation improvement
├── screenshots/             # Captured video frames
└── README.md               # This documentation
```

## Integration

The new system integrates seamlessly with the existing pipeline:

1. **Backward Compatibility**: Falls back to original method if Claude 4 fails
2. **Metadata Integration**: Uses existing AniList API integration
3. **File Processing**: Maintains existing file input/output structure
4. **Error Handling**: Preserves existing error handling and logging

## Benefits

### Translation Quality

- **Scene-Aware Chunking**: Respects narrative flow instead of arbitrary line counts
- **Visual Context**: Better handling of visual gags and cultural references
- **Iterative Improvement**: Multiple passes ensure higher quality
- **Character Consistency**: Maintains speech patterns across episodes

### Efficiency

- **Intelligent Chunking**: Reduces API calls by optimizing chunk sizes
- **Context Reuse**: Avoids redundant character and terminology lookups
- **Fallback System**: Ensures reliability with backup translation method
- **Progress Tracking**: Provides detailed progress information

### Maintainability

- **Modular Design**: Separate tools for different translation aspects
- **Tool-Based Architecture**: Easy to extend with new capabilities
- **Comprehensive Logging**: Detailed logging for debugging and monitoring
- **Configuration Options**: Flexible settings for different use cases

## Troubleshooting

### Common Issues

1. **Video File Not Found**: Screenshots disabled automatically if video unavailable
2. **API Rate Limits**: Built-in retry logic with exponential backoff
3. **Scene Detection Errors**: Falls back to original chunking method
4. **Translation Quality Issues**: Correction tool attempts to improve automatically

### Debug Mode

Enable detailed logging by setting environment variables:

```bash
DEBUG=true
VERBOSE_LOGGING=true
```

### Performance Optimization

- Disable screenshots for faster processing: `enableScreenshots: false`
- Reduce correction iterations: `maxIterations: 1`
- Adjust scene detection sensitivity: modify weight parameters

## Future Enhancements

- **OCR Integration**: Extract and translate text from video frames
- **Audio Analysis**: Use audio cues for better scene detection
- **Machine Learning**: Train models on translation patterns
- **Real-time Processing**: Support for live translation streams
- **Multi-language Support**: Extend to other target languages
