// @ts-check

/**
 * Configuration for Claude 4 Translation System
 * 
 * This file contains all configurable parameters for the translation system.
 * Modify these values to customize the behavior according to your needs.
 */

export const CLAUDE4_CONFIG = {
  // Claude 4 API Settings
  model: "claude-sonnet-4-20250514",
  maxTokens: 8192,
  temperature: 0.7,
  maxRetries: 3,
  retryDelay: 2000, // milliseconds

  // Translation Features
  enableScreenshots: true,
  enableCorrection: true,
  enableMetadataFetching: true,
  enableFallback: true,

  // Scene Detection Settings
  sceneDetection: {
    minSceneLength: 2,        // Minimum lines per scene
    maxSceneLength: 80,       // Maximum lines per scene
    timingGapThreshold: 5000, // Timing gap threshold in milliseconds
    speakerChangeWeight: 0.3, // Weight for speaker changes (0-1)
    timingWeight: 0.4,        // Weight for timing gaps (0-1)
    contentWeight: 0.3,       // Weight for content transitions (0-1)
    qualityThreshold: 0.3     // Minimum quality threshold for scene breaks
  },

  // Screenshot Settings
  screenshot: {
    outputDir: 'app/2translate/screenshots',
    quality: 2,               // FFmpeg quality scale (1-31, lower is better)
    format: 'png',            // Output format (png, jpg)
    maxWidth: 1280,           // Maximum width for screenshots
    cleanupAge: 24 * 60 * 60 * 1000, // Cleanup age in milliseconds (24 hours)
    enableAnalysis: true,     // Enable image analysis
    captureSequence: false    // Capture multiple frames around timestamp
  },

  // Correction Settings
  correction: {
    maxIterations: 2,         // Maximum correction iterations
    qualityThreshold: 0.8,    // Quality threshold to stop iterations (0-1)
    enableGrammarCheck: true, // Enable Polish grammar checking
    enableConsistencyCheck: true, // Enable character consistency checking
    enableCulturalAdaptation: true, // Enable cultural adaptation
    
    // Quality weights for overall score calculation
    qualityWeights: {
      grammar: 0.25,
      naturalness: 0.25,
      accuracy: 0.25,
      consistency: 0.15,
      culturalAdaptation: 0.10
    }
  },

  // Context Management
  context: {
    maxSceneHistory: 10,      // Maximum scenes to keep in history
    enableTerminologyTracking: true, // Track terminology consistency
    enableCharacterAnalysis: true,   // Analyze character speech patterns
    enableProgressTracking: true     // Track translation progress
  },

  // File Processing
  files: {
    inputDirectory: 'app/2translate/toTranslate',
    outputDirectory: 'app/3replace/withActors',
    videoDirectory: 'app/0rss/downloads',
    assDirectory: 'app/1clear/extracted',
    supportedVideoFormats: ['.mkv', '.mp4', '.avi'],
    backupOriginals: true     // Keep backup of original files
  },

  // Logging and Debugging
  logging: {
    enableDetailedLogging: true,
    enablePerformanceMetrics: true,
    enableTranslationStats: true,
    logLevel: 'debug',         // 'debug', 'info', 'warn', 'error'
    saveTranslationLogs: true
  },

  // Performance Optimization
  performance: {
    enableCaching: true,      // Cache translation results
    enableParallelProcessing: false, // Process multiple scenes in parallel
    maxConcurrentRequests: 1, // Maximum concurrent API requests
    requestTimeout: 60000,    // Request timeout in milliseconds
    enableRateLimiting: true  // Enable rate limiting for API calls
  },

  // Polish Language Specific Settings
  polish: {
    preserveHonorifics: true, // Keep Japanese honorifics uninflected
    enableFormalityDetection: true, // Detect and maintain formality levels
    enableGenderAgreement: true,     // Ensure proper gender agreement
    enableCaseCorrection: true,      // Correct Polish case declension
    
    // Common Polish translation preferences
    translationPreferences: {
      usePolishIdioms: true,          // Adapt idioms to Polish equivalents
      avoidLiteralTranslations: true, // Avoid word-for-word translations
      maintainCharacterVoice: true,   // Preserve character speech patterns
      adaptCulturalReferences: true   // Adapt cultural references for Polish audience
    }
  },

  // Error Handling
  errorHandling: {
    enableFallbackTranslation: true, // Use original method if Claude 4 fails
    enableErrorRecovery: true,       // Attempt to recover from errors
    maxErrorRetries: 3,              // Maximum retries for error recovery
    enableErrorReporting: true,      // Report errors to Discord webhook
    continueOnError: true            // Continue processing other files on error
  },

  // Experimental Features
  experimental: {
    enableOCR: false,               // Extract text from video frames (requires OCR)
    enableAudioAnalysis: false,     // Use audio for scene detection
    enableMLEnhancement: false,     // Use ML models for enhancement
    enableRealtimeProcessing: false // Support real-time translation
  }
};

// Environment-specific overrides
export function getConfig(environment = 'production') {
  const config = { ...CLAUDE4_CONFIG };

  switch (environment) {
    case 'development':
      config.logging.logLevel = 'debug';
      config.logging.enableDetailedLogging = true;
      config.maxRetries = 1;
      config.correction.maxIterations = 1;
      config.enableScreenshots = false; // Faster for development
      break;

    case 'testing':
      config.enableScreenshots = false;
      config.enableCorrection = false;
      config.maxRetries = 1;
      config.logging.enableDetailedLogging = false;
      config.performance.enableCaching = false;
      break;

    case 'production':
      // Use default production settings
      break;

    default:
      console.warn(`Unknown environment: ${environment}, using production config`);
  }

  return config;
}

// Validation function to ensure config is valid
export function validateConfig(config) {
  const errors = [];

  // Validate required API key (only in production)
  if (!process.env.ANTHROPIC_API_KEY && process.env.NODE_ENV === 'production') {
    errors.push('ANTHROPIC_API_KEY environment variable is required');
  }

  // Validate numeric ranges
  if (config.temperature < 0 || config.temperature > 1) {
    errors.push('temperature must be between 0 and 1');
  }

  if (config.sceneDetection.minSceneLength >= config.sceneDetection.maxSceneLength) {
    errors.push('minSceneLength must be less than maxSceneLength');
  }

  if (config.correction.qualityThreshold < 0 || config.correction.qualityThreshold > 1) {
    errors.push('qualityThreshold must be between 0 and 1');
  }

  // Validate weights sum to 1
  const weights = Object.values(config.correction.qualityWeights);
  const weightSum = weights.reduce((sum, weight) => sum + weight, 0);
  if (Math.abs(weightSum - 1.0) > 0.01) {
    errors.push('qualityWeights must sum to 1.0');
  }

  // Skip directory validation for now to avoid async issues
  // This can be added back later with proper async handling

  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
  }

  return true;
}

// Helper function to get environment-specific config with validation
export function getValidatedConfig(environment) {
  const config = getConfig(environment);
  validateConfig(config);
  return config;
}

// Export default config for convenience
export default CLAUDE4_CONFIG;
